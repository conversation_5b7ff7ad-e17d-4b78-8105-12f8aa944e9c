"""
Accelerate Analysis Pipeline - Modularized Version

This package contains the modularized version of the Accelerate analysis pipeline
for retail analytics and campaign effectiveness measurement.

Modules:
- config: Configuration settings and global variables
- utils: Utility functions for date handling and data validation
- data_processing: Data loading, cleaning, and preprocessing functions
- analysis: Statistical analysis, clustering, and calculation functions
- main: Main orchestrator and execution logic

Usage:
    from accelerate_modules.main import run_accelerate_pipeline
    run_accelerate_pipeline()

Or import specific functions:
    from accelerate_modules.analysis import get_uplift
    from accelerate_modules.data_processing import create_pilot_df
"""

from .main import run_accelerate_pipeline, accelerate
from .config import E_path, final_result_output
from .data_processing import create_pilot_df, get_sku_list, create_val_data
from .analysis import get_uplift, significance_level, lift_outlier_iqr
from .utils import find_date, get_activation_week_index

__version__ = "1.0.0"
__author__ = "Accelerate Team"

__all__ = [
    'run_accelerate_pipeline',
    'accelerate',
    'create_pilot_df',
    'get_sku_list',
    'create_val_data',
    'get_uplift',
    'significance_level',
    'lift_outlier_iqr',
    'find_date',
    'get_activation_week_index',
    'E_path',
    'final_result_output'
]
