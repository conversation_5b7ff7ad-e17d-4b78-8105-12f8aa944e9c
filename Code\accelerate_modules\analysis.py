"""
Analysis module for the Accelerate analysis pipeline.
Contains functions for statistical analysis, clustering, and calculations.
"""

import pandas as pd
import numpy as np
import time
import math
import sklearn.metrics
from sklearn.metrics import silhouette_score
from sklearn.cluster import KMeans
from dtaidistance import dtw
from scipy.stats import wilcoxon
from .config import E_path, UPPER_LIFT_BOUND, LOWER_LIFT_BOUND, ALPHA_SIGNIFICANCE


def testvscontrolfcn(control_data, test_data):
    """
    Compare test vs control data means.
    
    Args:
        control_data: Control group data
        test_data: Test group data
        
    Returns:
        DataFrame: Combined test and control means
    """
    # Drop known non-numeric or irrelevant columns
    drop_columns = ['Start_Date', 'End_Date', 'NET_AVG_Y1', 'POC_ID']
    control_data = control_data.drop(columns=[col for col in drop_columns if col in control_data.columns])
    test_data = test_data.drop(columns=[col for col in drop_columns if col in test_data.columns])

    # Replace 0 with NaN
    control_data = control_data.replace(0, np.nan)
    test_data = test_data.replace(0, np.nan)

    # Select only numeric columns (to avoid TypeError)
    control_data = control_data.select_dtypes(include='number')
    test_data = test_data.select_dtypes(include='number')

    # Compute means
    test_mean = test_data.mean(axis=0, skipna=True).to_frame().transpose()
    control_mean = control_data.mean(axis=0, skipna=True).to_frame().transpose()

    # Align column order (optional but safe)
    control_mean = control_mean[test_mean.columns]

    # Combine test and control means
    test_control_mean = pd.concat([test_mean, control_mean], ignore_index=True)
    test_control_mean.rename(index={0: 'Test', 1: 'Control'}, inplace=True)

    return test_control_mean


def get_dist_mat_grp(dm_data_grp):
    """
    Calculate distance matrices for data groups.
    
    Args:
        dm_data_grp: List of data groups
        
    Returns:
        list: Distance matrices for each group
    """
    vol_grp_cnt = 1
    dist_mat_grp = []
    dm_data_len = len(dm_data_grp)
    
    for dm_vg in dm_data_grp:
        try:
            t1 = time.time()
            np.random.seed(47)
            _dm = dtw.distance_matrix(dm_vg.values, parallel=False)

            _nan_val = np.isnan(_dm)
            _inf_val = np.isinf(_dm)

            _dm[_nan_val] = 0
            _dm[_inf_val] = 0

            dist_mat_grp.append(_dm)

            _disp_msg = "[DM] Took {} seconds [Volume-Group-" + str(vol_grp_cnt) + "]"
            print(_disp_msg.format(time.time() - t1))
            vol_grp_cnt = vol_grp_cnt + 1
        except Exception as e:
            print(e)
            print("Error in calculating distance matrix for Group-" + str(vol_grp_cnt))
            vol_grp_cnt = vol_grp_cnt + 1
            
    return dist_mat_grp


def get_optimal_n_cluster(dist_mat_grp):
    """
    Find optimal number of clusters for each distance matrix.
    
    Args:
        dist_mat_grp: List of distance matrices
        
    Returns:
        list: Optimal cluster numbers for each group
    """
    vol_grp_cnt = 1
    num_clusters_grp = []
    dist_mat_len = len(dist_mat_grp)

    for _di in range(0, dist_mat_len):
        t1 = time.time()
        max_sil_score = -1
        opt_clus = 1

        X = dist_mat_grp[_di]
        # decide min/max no of clusters
        for n_cluster in range(1, 3):
            try:
                kmeans = KMeans(n_clusters=n_cluster, random_state=47).fit(X)
                label = kmeans.labels_
                sil_coeff = silhouette_score(X, label, metric='euclidean')

                if sil_coeff > max_sil_score:
                    max_sil_score = sil_coeff
                    opt_clus = n_cluster

                print("\tFor n_clusters={}, The Silhouette Coefficient is {}".format(n_cluster, sil_coeff))
            except:
                print("\tError in finding optimal cluster for Group-" + str(1 + _di))

        num_clusters_grp.append(opt_clus)

        _disp_msg = "[Volume-Group-" + str(vol_grp_cnt) + "] | Optimal n-Cluster = " + str(opt_clus) + " | Took {} seconds"
        print(_disp_msg.format(time.time() - t1))
        print("---------------------------------------------------------------------------")
        vol_grp_cnt = vol_grp_cnt + 1
    
    return num_clusters_grp


def get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, DATA):
    """
    Generate clustered data using KMeans.
    
    Args:
        dist_mat_grp: Distance matrices
        num_clusters_grp: Number of clusters for each group
        date_grp: Date groups
        poc_ids_grp: POC ID groups
        kmeans_grp: KMeans models storage
        DATA: Original data
        
    Returns:
        list: Clustered data groups
    """
    index = 0
    vol_grp_cnt = 1
    clustered_data_grp = []
    
    for _dm in dist_mat_grp:
        t1 = time.time()
        kmeans = KMeans(n_clusters=num_clusters_grp[index], random_state=47).fit(_dm)
        kmeans_grp.append(kmeans)
        _labels = kmeans.predict(_dm)
        
        _data = DATA[DATA.POC_ID.isin(poc_ids_grp[index])]
        _data['Cluster'] = _labels.tolist()
        clustered_data_grp.append(_data)
        index = index + 1
        vol_grp_cnt = vol_grp_cnt + 1
        
    return clustered_data_grp


def filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit):
    """
    Filter control POCs based on RMSE similarity to test POC.

    Args:
        global_ctrl: Control group data
        global_test: Test group data
        min_index: Minimum index
        activation_on: Activation start index
        limit: Maximum number of control POCs

    Returns:
        DataFrame: Filtered control POCs
    """
    rmse_val = []
    control = global_ctrl
    ctrl_baseline = global_ctrl.iloc[:, min_index:activation_on]
    test_baseline = global_test.iloc[:, min_index:activation_on]
    # test dataframe
    predicted = test_baseline.iloc[0, :].tolist()

    for i in range(len(ctrl_baseline)):
        actual = ctrl_baseline.iloc[i, :].tolist()
        mse = sklearn.metrics.mean_squared_error(actual, predicted)
        rmse = math.sqrt(mse)
        rmse_val.append(rmse)

    control["RMSE Value"] = rmse_val
    control["RMSE_Rank"] = control["RMSE Value"].rank(method='min')
    control = control[control["RMSE_Rank"] <= limit]

    new_ctrl = global_ctrl[global_ctrl["POC_ID"].isin(control["POC_ID"])]
    return new_ctrl


def get_uplift(desc_test, desc_ctrl, min_index, activation_on, activation_end, test_poc, ctrl_pocs, ctrl_outliers, RESTRICT_BASELINE_TO, APT_RESULTS, campaign, retailer, sku):
    """
    Calculate uplift metrics for test vs control comparison.

    Args:
        desc_test: Test data description
        desc_ctrl: Control data description
        min_index: Minimum index
        activation_on: Activation start
        activation_end: Activation end
        test_poc: Test POC ID
        ctrl_pocs: Control POC IDs
        ctrl_outliers: Number of control outliers
        RESTRICT_BASELINE_TO: Baseline restriction
        APT_RESULTS: Results dataframe
        campaign: Campaign name
        retailer: Retailer name
        sku: SKU identifier

    Returns:
        list: [perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol]
    """
    activation_on = activation_on - min_index
    if isinstance(test_poc, pd.Series) or isinstance(test_poc, np.ndarray):
        test_poc = int(test_poc.iloc[0] if hasattr(test_poc, 'iloc') else test_poc[0])

    if len(desc_test) == len(desc_ctrl):
        nRecords = len(desc_test)
        sum_ctrl_y1 = 0
        sum_ctrl_y2 = 0
        sum_test_y1 = 0
        sum_test_y2 = 0

        ctrl_zero_y1 = 0
        ctrl_zero_y2 = 0
        test_zero_y1 = 0
        test_zero_y2 = 0
        baseline_start = 0

        if activation_on > RESTRICT_BASELINE_TO:
            baseline_start = activation_on - RESTRICT_BASELINE_TO

        for nRec in range(baseline_start, activation_on):
            sum_ctrl_y1 = sum_ctrl_y1 + desc_ctrl[nRec]
            sum_test_y1 = sum_test_y1 + desc_test[nRec]
            if (desc_ctrl[nRec] == 0):
                ctrl_zero_y1 = ctrl_zero_y1 + 1
            if (desc_test[nRec] == 0):
                test_zero_y1 = test_zero_y1 + 1

        analysis_end_index = (activation_end - min_index) + 1
        for nRec in range(activation_on, analysis_end_index):
            sum_ctrl_y2 = sum_ctrl_y2 + desc_ctrl[nRec]
            sum_test_y2 = sum_test_y2 + desc_test[nRec]
            if (desc_ctrl[nRec] == 0):
                ctrl_zero_y2 = ctrl_zero_y2 + 1
            if (desc_test[nRec] == 0):
                test_zero_y2 = test_zero_y2 + 1

        desc_ctrl_1 = desc_ctrl[baseline_start:activation_on]
        desc_ctrl_2 = desc_ctrl[activation_on:analysis_end_index]
        desc_test_1 = desc_test[baseline_start:activation_on]
        desc_test_2 = desc_test[activation_on:analysis_end_index]

        ba_count = activation_on - baseline_start
        aa_count = analysis_end_index - activation_on

        avg_ctrl_y1 = sum_ctrl_y1 / (ba_count - ctrl_zero_y1)
        avg_test_y1 = sum_test_y1 / (ba_count - test_zero_y1)

        avg_ctrl_y2 = sum_ctrl_y2 / (aa_count - ctrl_zero_y2)
        avg_test_y2 = sum_test_y2 / (aa_count - test_zero_y2)

        perc_inc_t = 100 * (avg_test_y2 - avg_test_y1) / avg_test_y1
        perc_inc_c = 100 * (avg_ctrl_y2 - avg_ctrl_y1) / avg_ctrl_y1

        test_expected = avg_test_y1 * (1 + (perc_inc_c / 100))
        lift = 100 * (avg_test_y2 - test_expected) / test_expected
        impact = avg_test_y2 - test_expected

        _avg_vol = (avg_test_y1 + avg_test_y2 + avg_ctrl_y1 + avg_ctrl_y2) / 4
        bs_mths = desc_ctrl_1.shape[0]
        as_mths = desc_ctrl_2.shape[0]

        if np.isinf(lift):
            lift = 0

        desc_test_avg = (desc_test[:-(activation_end - activation_on + 1)].mean())
        desc_ctrl_avg = (desc_ctrl[:-(activation_end - activation_on + 1)].mean())
        desc_test_dev = []
        desc_ctrl_dev = []

        for j in range(len(desc_test)):
            desc_test_dev.append((desc_test[j] - desc_test_avg) / desc_test_avg)
        for j in range(len(desc_ctrl)):
            desc_ctrl_dev.append((desc_ctrl[j] - desc_ctrl_avg) / desc_ctrl_avg)

        diff = [abs(desc_test_dev[k] - desc_ctrl_dev[k]) for k in range(len(desc_test_dev))]
        score = sum(diff[:-(activation_end - activation_on + 1)])

        print("Type of APT_RESULTS:", type(APT_RESULTS))
        print("POC_ID column type:", APT_RESULTS['POC_ID'].dtype)
        print("test_poc type:", type(test_poc))

        try:
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-TEST AVG VOL'] = (avg_test_y1 + avg_test_y2) / 2
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-AVG VOL'] = _avg_vol
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-%Lift'] = lift
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Estimated impact'] = impact
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Test baseline period'] = avg_test_y1
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Test analysis period'] = avg_test_y2
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Test expected'] = test_expected
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-%Test performance'] = perc_inc_t

            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control baseline period'] = avg_ctrl_y1
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control analysis period'] = avg_ctrl_y2
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-%Control performance'] = perc_inc_c

            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Baseline period # weeks with data'] = bs_mths
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Analysis period # weeks with data'] = as_mths
            print("avg_ctrl_y1:", avg_ctrl_y1, "avg_test_y1:", avg_test_y1)
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control count'] = len(ctrl_pocs)
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control Outliers'] = ctrl_outliers
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Goodness of fit score'] = score
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'Campaign'] = campaign
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'Retailer'] = retailer
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'SKU'] = sku
        except Exception as e:
            print("Error after Seven:", e)

        APT_RESULTS.to_excel(E_path + "\\APT_test.xlsx")
    else:
        print("ERROR : Test and Control are not having same number of columns")

    print(perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol)

    return [perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol]


def get_uplift_val(desc_test, desc_ctrl, min_index, activation_on, validation_start, validation_end, test_poc, ctrl_pocs, RESTRICT_BASELINE_TO, APT_RESULTS):
    """
    Calculate validation period uplift metrics.

    Args:
        desc_test: Test data description
        desc_ctrl: Control data description
        min_index: Minimum index
        activation_on: Activation start
        validation_start: Validation period start
        validation_end: Validation period end
        test_poc: Test POC ID
        ctrl_pocs: Control POC IDs
        RESTRICT_BASELINE_TO: Baseline restriction
        APT_RESULTS: Results dataframe

    Returns:
        list: [lift] validation period lift
    """
    if isinstance(test_poc, pd.Series) or isinstance(test_poc, np.ndarray):
        test_poc = int(test_poc.iloc[0] if hasattr(test_poc, 'iloc') else test_poc[0])
    activation_on = activation_on - min_index

    if len(desc_test) == len(desc_ctrl):
        nRecords = len(desc_test)
        sum_ctrl_y1 = 0
        sum_ctrl_y2 = 0
        sum_test_y1 = 0
        sum_test_y2 = 0

        ctrl_zero_y1 = 0
        ctrl_zero_y2 = 0
        test_zero_y1 = 0
        test_zero_y2 = 0

        baseline_start = 0
        if activation_on > RESTRICT_BASELINE_TO:
            baseline_start = activation_on - RESTRICT_BASELINE_TO

        for nRec in range(baseline_start, activation_on):
            sum_ctrl_y1 = sum_ctrl_y1 + desc_ctrl[nRec]
            sum_test_y1 = sum_test_y1 + desc_test[nRec]
            if (desc_ctrl[nRec] == 0):
                ctrl_zero_y1 = ctrl_zero_y1 + 1
            if (desc_test[nRec] == 0):
                test_zero_y1 = test_zero_y1 + 1

        analysis_end_index = (validation_end - min_index) + 1

        for nRec in range(activation_on, analysis_end_index):
            sum_ctrl_y2 = sum_ctrl_y2 + desc_ctrl[nRec]
            sum_test_y2 = sum_test_y2 + desc_test[nRec]
            if (desc_ctrl[nRec] == 0):
                ctrl_zero_y2 = ctrl_zero_y2 + 1
            if (desc_test[nRec] == 0):
                test_zero_y2 = test_zero_y2 + 1

        desc_ctrl_1 = desc_ctrl[baseline_start:activation_on]
        desc_ctrl_2 = desc_ctrl[activation_on:analysis_end_index]
        desc_test_1 = desc_test[baseline_start:activation_on]
        desc_test_2 = desc_test[activation_on:analysis_end_index]

        ba_count = activation_on - baseline_start
        aa_count = analysis_end_index - activation_on

        avg_ctrl_y1 = sum_ctrl_y1 / (ba_count - ctrl_zero_y1)
        avg_test_y1 = sum_test_y1 / (ba_count - test_zero_y1)

        avg_ctrl_y2 = sum_ctrl_y2 / (aa_count - ctrl_zero_y2)
        avg_test_y2 = sum_test_y2 / (aa_count - test_zero_y2)

        perc_inc_t = 100 * (avg_test_y2 - avg_test_y1) / avg_test_y1
        perc_inc_c = 100 * (avg_ctrl_y2 - avg_ctrl_y1) / avg_ctrl_y1

        test_expected = avg_test_y1 * (1 + (perc_inc_c / 100))
        lift = 100 * (avg_test_y2 - test_expected) / test_expected
        impact = avg_test_y2 - test_expected

        _avg_vol = (avg_test_y1 + avg_test_y2 + avg_ctrl_y1 + avg_ctrl_y2) / 4
        bs_mths = desc_ctrl_1.shape[0]
        as_mths = desc_ctrl_2.shape[0]

        if np.isinf(lift):
            lift = 0

        APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-% Validation Period Lift'] = lift
        APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Validation Period Impact'] = impact

    else:
        print("ERROR : Test and Control are not having same number of columns")

    return [lift]


def lift_outlier_iqr(data):
    """
    Identify outliers in lift data using IQR method and business rules.

    Args:
        data: DataFrame with lift data

    Returns:
        DataFrame: Data with outlier flags
    """
    upper_bound = UPPER_LIFT_BOUND
    lower_bound = LOWER_LIFT_BOUND

    data['Outlier'] = data['ABI-%Lift'].apply(lambda x: "Yes" if (x > upper_bound or x < lower_bound) else "No")
    data['Outlier_Reason'] = data['ABI-%Lift'].apply(lambda x: "Uplift beyond threshold" if (x > upper_bound or x < lower_bound) else "")

    data.loc[(data['ABI-Test analysis period'].isna()), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Test analysis period'].isna()), 'Outlier_Reason'] = "Test site has zero-valued in the analysis period"

    data.loc[(data['ABI-Control analysis period'].isna()), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Control analysis period'].isna()), 'Outlier_Reason'] = "Control site has zero-valued in the analysis period"

    data.loc[(data['ABI-Test baseline period'].isna()), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Test baseline period'].isna()), 'Outlier_Reason'] = "Test site has zero-valued in the baseline period"

    data.loc[(data['ABI-Control baseline period'].isna()), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Control baseline period'].isna()), 'Outlier_Reason'] = "Control site has zero-valued in the baseline period"

    data.loc[(data['ABI-Test analysis period'] < 0), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Test analysis period'] < 0), 'Outlier_Reason'] = "Test site has negative data in the analysis period"

    data.loc[(data['ABI-Control analysis period'] < 0), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Control analysis period'] < 0), 'Outlier_Reason'] = "Control site has negative data in the analysis period"

    data.loc[(data['ABI-Test baseline period'] < 0), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Test baseline period'] < 0), 'Outlier_Reason'] = "Test site has negative data in the baseline period"

    data.loc[(data['ABI-Control baseline period'] < 0), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Control baseline period'] < 0), 'Outlier_Reason'] = "Control site has negative data in the baseline period"

    data.loc[(data['ABI-Control count'].isna()), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Control count'].isna()), 'Outlier_Reason'] = "Test site has control POCs below 5"

    data.loc[(data['ABI-Control count'] < 5), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Control count'] < 5), 'Outlier_Reason'] = "Test site has control POCs below 5"

    return data


def significance_level(data, APT_RESULTS, sku):
    """
    Calculate statistical significance level using Wilcoxon test.

    Args:
        data: Filtered data without outliers
        APT_RESULTS: Results dataframe
        sku: SKU identifier

    Returns:
        float: Significance level percentage
    """
    # Filter outliers & nulls
    APT_RESULTS_NO_OUTLIER = data[
        (data["Outlier"] == "No") &
        ~(data["ABI-% Validation Period Lift"].isnull())
    ]

    # Prepare the two samples
    data1 = APT_RESULTS_NO_OUTLIER["ABI-% Validation Period Lift"]
    data2 = APT_RESULTS_NO_OUTLIER["ABI-%Lift"]

    print(f"Running Wilcoxon test for SKU: {repr(sku)}")
    print(f"Sample sizes → data1: {len(data1)}, data2: {len(data2)}")

    # Compare samples
    stat, p = wilcoxon(data1, data2)
    print(f"Statistics={stat:.3f}, p={p:.3f}")

    significance_val = (1 - p) * 100
    print(f"Significance level = {significance_val:.2f}%")

    # Create the column if it doesn't exist
    if 'Significance' not in APT_RESULTS.columns:
        APT_RESULTS['Significance'] = None

    # Assign in place
    mask = APT_RESULTS['SKU'].astype(int) == int(sku)
    APT_RESULTS.loc[mask, 'Significance'] = significance_val

    # Check result
    print(APT_RESULTS.loc[mask, ['SKU', 'Significance']])

    # Interpret result
    alpha = ALPHA_SIGNIFICANCE
    if p > alpha:
        print('Same distribution (fail to reject H0)')
    else:
        print('Different distribution (reject H0)')

    return significance_val
