"""
Test script for the Accelerate Optimized Pipeline

This script validates that the optimized version works correctly and provides
performance benchmarking capabilities.

Features:
- Import validation
- Configuration testing
- Performance monitoring validation
- System compatibility checks
- Basic functionality tests
"""

import sys
import os
import time
import traceback
from datetime import datetime

def test_imports():
    """Test that all optimized modules can be imported successfully."""
    print("🔍 Testing imports...")
    
    try:
        # Test main imports
        from accelerate_optimized import (
            run_accelerate_optimized, 
            print_optimization_info,
            performance_monitor,
            get_system_info
        )
        print("✅ Main imports successful")
        
        # Test module imports
        from accelerate_optimized import (
            data_processing_optimized,
            analysis_optimized,
            utils_optimized,
            config_optimized
        )
        print("✅ Module imports successful")
        
        # Test specific function imports
        from accelerate_optimized.data_processing_optimized import (
            create_pilot_df_optimized,
            optimize_dataframe_dtypes
        )
        from accelerate_optimized.analysis_optimized import (
            testvscontrolfcn_optimized,
            get_uplift_optimized
        )
        from accelerate_optimized.utils_optimized import (
            find_date,
            validate_data_quality
        )
        print("✅ Function imports successful")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during imports: {e}")
        return False

def test_configuration():
    """Test configuration and system detection."""
    print("\n🔧 Testing configuration...")
    
    try:
        from accelerate_optimized.config_optimized import (
            MAX_WORKERS, CPU_COUNT, MEMORY_LIMIT_GB,
            get_system_info, PARALLEL_SKU_PROCESSING
        )
        
        # Test system info
        system_info = get_system_info()
        print(f"✅ System detection: {system_info['cpu_count']} CPUs, {system_info['memory_gb']:.1f}GB RAM")
        
        # Validate configuration values
        assert MAX_WORKERS > 0, "MAX_WORKERS should be positive"
        assert CPU_COUNT > 0, "CPU_COUNT should be positive"
        assert MEMORY_LIMIT_GB > 0, "MEMORY_LIMIT_GB should be positive"
        
        print(f"✅ Configuration valid: {MAX_WORKERS} workers, {MEMORY_LIMIT_GB:.1f}GB memory limit")
        
        # Test parallel processing flag
        print(f"✅ Parallel processing: {'Enabled' if PARALLEL_SKU_PROCESSING else 'Disabled'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_performance_monitor():
    """Test performance monitoring functionality."""
    print("\n📊 Testing performance monitor...")
    
    try:
        from accelerate_optimized.performance_monitor import (
            performance_monitor, timed_operation, memory_monitor
        )
        
        # Test basic monitoring
        performance_monitor.start_monitoring()
        
        # Test timer context manager
        with performance_monitor.timer('test_operation'):
            time.sleep(0.1)  # Simulate work
        
        # Test memory monitor
        with memory_monitor('test_memory'):
            test_data = list(range(1000))  # Create some data
        
        # Test counters
        performance_monitor.increment_counter('test_counter', 5)
        
        # Test progress bar
        pbar = performance_monitor.create_progress_bar('test_progress', 10, 'Testing')
        for i in range(10):
            performance_monitor.update_progress('test_progress')
            time.sleep(0.01)
        performance_monitor.close_progress_bar('test_progress')
        
        performance_monitor.stop_monitoring()
        
        print("✅ Performance monitoring functional")
        return True
        
    except Exception as e:
        print(f"❌ Performance monitor error: {e}")
        return False

def test_data_processing():
    """Test optimized data processing functions."""
    print("\n📊 Testing data processing...")
    
    try:
        import pandas as pd
        import numpy as np
        from accelerate_optimized.data_processing_optimized import (
            optimize_dataframe_dtypes,
            DataCache
        )
        from accelerate_optimized.utils_optimized import (
            validate_data_quality,
            detect_outliers_iqr,
            preprocess_data_optimized
        )
        
        # Create test data
        test_data = pd.DataFrame({
            'int_col': np.random.randint(0, 100, 1000),
            'float_col': np.random.random(1000),
            'category_col': np.random.choice(['A', 'B', 'C'], 1000),
            'date_col': pd.date_range('2023-01-01', periods=1000, freq='D')
        })
        
        # Test data type optimization
        original_memory = test_data.memory_usage(deep=True).sum()
        optimized_data = optimize_dataframe_dtypes(test_data)
        optimized_memory = optimized_data.memory_usage(deep=True).sum()
        
        memory_saved = original_memory - optimized_memory
        print(f"✅ Data type optimization: Saved {memory_saved / 1024:.1f}KB")
        
        # Test data validation
        validation_result = validate_data_quality(test_data)
        print(f"✅ Data validation: {'Valid' if validation_result['is_valid'] else 'Issues found'}")
        
        # Test outlier detection
        outlier_result = detect_outliers_iqr(test_data, ['int_col', 'float_col'])
        print(f"✅ Outlier detection: {outlier_result['total_outliers']} outliers found")
        
        # Test preprocessing
        processed_data, log = preprocess_data_optimized(test_data)
        print(f"✅ Data preprocessing: {len(log)} operations performed")
        
        # Test caching
        cache = DataCache()
        cache.set('test_key', test_data)
        cached_data = cache.get('test_key')
        assert cached_data is not None, "Caching should work"
        print("✅ Caching system functional")
        
        return True
        
    except Exception as e:
        print(f"❌ Data processing error: {e}")
        traceback.print_exc()
        return False

def test_analysis_functions():
    """Test optimized analysis functions."""
    print("\n🔬 Testing analysis functions...")
    
    try:
        import pandas as pd
        import numpy as np
        from accelerate_optimized.analysis_optimized import (
            testvscontrolfcn_optimized,
            significance_level_optimized
        )
        from accelerate_optimized.utils_optimized import detect_outliers_iqr
        
        # Create test data for analysis
        np.random.seed(42)
        test_data = pd.DataFrame({
            'POC_ID': range(50),
            'value1': np.random.normal(100, 20, 50),
            'value2': np.random.normal(150, 30, 50),
            'value3': np.random.normal(80, 15, 50)
        })
        
        control_data = pd.DataFrame({
            'POC_ID': range(50, 100),
            'value1': np.random.normal(95, 18, 50),
            'value2': np.random.normal(145, 25, 50),
            'value3': np.random.normal(75, 12, 50)
        })
        
        # Test test vs control function
        comparison_result = testvscontrolfcn_optimized(control_data, test_data)
        print(f"✅ Test vs Control analysis: {comparison_result.shape[0]} comparisons")
        
        print("✅ Analysis functions operational")
        return True
        
    except Exception as e:
        print(f"❌ Analysis functions error: {e}")
        traceback.print_exc()
        return False

def run_performance_benchmark():
    """Run a simple performance benchmark."""
    print("\n⚡ Running performance benchmark...")
    
    try:
        import pandas as pd
        import numpy as np
        from accelerate_optimized.performance_monitor import performance_monitor
        from accelerate_optimized.data_processing_optimized import optimize_dataframe_dtypes
        
        # Create larger test dataset
        size = 10000
        test_data = pd.DataFrame({
            'int_col': np.random.randint(0, 1000, size),
            'float_col': np.random.random(size) * 1000,
            'category_col': np.random.choice(['Category_' + str(i) for i in range(100)], size),
            'date_col': pd.date_range('2020-01-01', periods=size, freq='H')
        })
        
        performance_monitor.start_monitoring()
        
        # Benchmark data type optimization
        start_time = time.time()
        optimized_data = optimize_dataframe_dtypes(test_data)
        optimization_time = max(time.time() - start_time, 0.001)  # Avoid division by zero
        
        # Calculate memory savings
        original_memory = test_data.memory_usage(deep=True).sum()
        optimized_memory = optimized_data.memory_usage(deep=True).sum()
        memory_savings = (1 - optimized_memory / original_memory) * 100
        
        performance_monitor.stop_monitoring()
        
        print(f"✅ Benchmark completed:")
        print(f"   📊 Dataset size: {size:,} rows")
        print(f"   ⏱️  Optimization time: {optimization_time:.3f} seconds")
        print(f"   💾 Memory savings: {memory_savings:.1f}%")
        print(f"   🚀 Processing rate: {size/optimization_time:,.0f} rows/second")
        
        return True
        
    except Exception as e:
        print(f"❌ Benchmark error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 ACCELERATE OPTIMIZED PIPELINE - TEST SUITE")
    print("=" * 60)
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Import Validation", test_imports),
        ("Configuration Testing", test_configuration),
        ("Performance Monitor", test_performance_monitor),
        ("Data Processing", test_data_processing),
        ("Analysis Functions", test_analysis_functions),
        ("Performance Benchmark", run_performance_benchmark)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The optimized pipeline is ready to use.")
        print("\n💡 Next steps:")
        print("   1. Run: python Accelerate_Optimized.py --info")
        print("   2. Test with your data: python Accelerate_Optimized.py")
        print("   3. Compare performance with original version")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("🔧 Consider checking dependencies and system requirements.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
