{"execution_summary": {"total_execution_time": 1.049769401550293, "peak_memory_usage_gb": 15.361709594726562, "total_operations": 5, "operations_per_second": 4.762950789588676, "garbage_collections": 0}, "timing_breakdown": {"test_operation": {"total_time": 0.1151120662689209, "average_time": 0.1151120662689209, "min_time": 0.1151120662689209, "max_time": 0.1151120662689209, "count": 1, "std_dev": 0.0}}, "memory_analysis": {"peak_memory_gb": 15.361709594726562, "average_memory_gb": 15.361709594726562, "min_memory_gb": 15.361709594726562, "memory_variance": 0.0, "memory_trend": "decreasing"}, "resource_utilization": {"average_cpu_percent": 0.0, "peak_cpu_percent": 0.0, "cpu_utilization_efficiency": "low"}, "bottlenecks": [], "recommendations": ["High memory usage detected - consider processing data in smaller chunks", "Low CPU utilization - consider increasing parallel processing"]}