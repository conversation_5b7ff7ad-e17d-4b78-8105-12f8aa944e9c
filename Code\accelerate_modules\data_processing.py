"""
Data processing module for the Accelerate analysis pipeline.
Contains functions for data loading, cleaning, and preprocessing.
"""

import os
import pandas as pd
import numpy as np
from .config import E_path


def create_pilot_df(activity_id, retailer):
    """
    Create pilot dataframe combining test and control stores.
    
    Args:
        activity_id: Campaign activity ID
        retailer: Retailer name
        
    Returns:
        DataFrame: Combined pilot dataframe
    """
    test_list = pd.read_excel(E_path + "\\Test Store List.xlsx").query("Campaign_ID == @activity_id")
    test_list['TestvControl'] = "Test"
    
    store_list = pd.read_excel(E_path + "\\Store Codes" + f"\\{retailer}.xlsx")
    control_list = store_list[~store_list['Store code applied by the retailer'].isin(test_list['Store_Id'])]
    control_list['TestvControl'] = "Control"
    control_list = control_list.rename(columns={'Store code applied by the retailer': 'Store_Id'})
    control_list.insert(0, 'Campaign_ID', activity_id)
    
    print(control_list.head())
    pilot_df = pd.concat([test_list, control_list], ignore_index=True)
    print(pilot_df['TestvControl'].unique())
    pilot_df.to_excel(E_path + "\\pilot_df.xlsx")
    
    return pilot_df


def get_sku_list(retailer, brand):
    """
    Get SKU list for specific retailer and brand.
    
    Args:
        retailer: Retailer name
        brand: Brand name
        
    Returns:
        DataFrame: Filtered SKU list
    """
    sku_list = pd.read_csv(E_path + "\\SKU List.csv").query("Retailer == @retailer and BRAND == @brand")
    return sku_list


def create_val_data(sku, retailer):
    """
    Create validation data for specific SKU and retailer.
    
    Args:
        sku: SKU identifier
        retailer: Retailer name
        
    Returns:
        DataFrame: Processed store data
    """
    val_path = os.path.join(E_path, 'SKU DATA', f'{retailer}')
    store_df = pd.read_csv(val_path + f"\\{sku}.csv")
    print("Successful read")
    
    store_df.rename(columns={
        'Store_Code': 'POC SAPID',
        'Store Code applied by the retailer': 'Store_Code',
        'Sales_Value': 'Sum(Value)',
        'Sales_Units': 'Sales_Units',
        'WEEK': 'Date'
    }, inplace=True)
    
    store_df['Date'] = pd.to_datetime(store_df['Date'], errors='coerce')
    store_df['Year'] = store_df['Date'].dt.year
    store_df['Month'] = store_df['Date'].dt.month
    store_df['Period_Date'] = pd.to_datetime(store_df[['Year', 'Month']].assign(Day=1))
    
    return store_df


def mod1(store_df):
    """
    Process store data with aggregation and pivoting.
    
    Args:
        store_df: Raw store dataframe
        
    Returns:
        DataFrame: Processed and pivoted data
    """
    data = store_df
    data['POC SAPID'] = data['POC SAPID'].astype(str)
    data = pd.concat([data, data], ignore_index=True)
    
    list_finaldata = list(data)
    mod_cols = [x.replace(' ', '_') for x in list_finaldata]
    data.columns = mod_cols
    
    data['Sum(Value)'] = data['Sum(Value)'].apply(lambda x: x if x >= 0 else 0)
    data['Date'] = pd.to_datetime(data['Date'], dayfirst=False)
    data['Year'] = pd.DatetimeIndex(data['Date']).year
    data['Month'] = pd.DatetimeIndex(data['Date']).month
    data['Month'] = data['Month'].astype('str')
    data['Month'] = data.Month.str.pad(2, side='left', fillchar='0')
    
    data = data.groupby(["POC_SAPID", "Month", "Date"]).agg({"Sum(Value)": "sum"}).reset_index()
    
    data_groupby = ['POC_SAPID']
    ident_cols = ['POC_SAPID', 'Month', 'Date']
    exp_cols = ['POC_SAPID']
    val_cols = ['Sum(Value)']
    
    molten_data = pd.melt(data, id_vars=ident_cols, value_vars=val_cols).sort_values(by=['POC_SAPID', 'Month', 'Date'])
    molten_data['MY'] = molten_data['Date'].astype('str')
    molten_data = molten_data.sort_values(by=['POC_SAPID', 'Month', 'Date'])
    
    Module1_data = molten_data.pivot(index='POC_SAPID', columns='MY', values='value')
    Module1_data.reset_index(inplace=True)
    
    return Module1_data


def read_data(Base_data, meta):
    """
    Read and merge base data with metadata.
    
    Args:
        Base_data: Base dataframe from previous module
        meta: Metadata dataframe
        
    Returns:
        list: [DATA, META] processed dataframes
    """
    DATA = Base_data
    nd_cols = [x.replace(' ', '_') for x in DATA.columns]
    DATA.columns = nd_cols
    DATA = DATA.rename(columns={'POC_SAPID': 'POC_ID'})
    DATA = DATA.dropna(subset=['POC_ID'])
    DATA.POC_ID = DATA.POC_ID.astype(int)
    DATA.POC_ID = DATA.POC_ID.astype(str)
    
    META = meta
    META = META.rename(columns={
        'Store number': 'POC_ID',
        'START DATE': 'Start_Date',
        'END DATE': 'End_Date',
        'TestvControl': 'Test_Control'
    })
    META = META.drop_duplicates(subset=['POC_ID', 'Start_Date', 'End_Date'])
    
    print("Hi")
    print(META['Test_Control'].unique())
    
    META['Start_Date'] = pd.to_datetime(META['Start_Date'], dayfirst=True)
    META['End_Date'] = pd.to_datetime(META['End_Date'], dayfirst=True)
    
    nd_cols = [x.replace(' ', '_') for x in META.columns]
    META.columns = nd_cols
    META = META.dropna(subset=['POC_ID'])
    META.POC_ID = META.POC_ID.astype(int)
    META.POC_ID = META.POC_ID.astype(str)
    
    try:
        DATA = pd.merge(DATA, META[['POC_ID', 'Start_Date', 'End_Date', 'Test_Control']], on=['POC_ID'], how='right')
        d_cols = list(DATA)
        DATA.columns = d_cols
    except Exception as e:
        print("Error in mapping Activation Date to Volume-Data", e)
    
    return [DATA, META]
