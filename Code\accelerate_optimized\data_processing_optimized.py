"""
Optimized data processing module for the Accelerate analysis pipeline.
Implements vectorized operations, caching, parallel data loading, and memory optimization.

Performance Improvements:
- Vectorized pandas operations
- Parallel data loading with ThreadPoolExecutor
- LRU caching for frequently accessed data
- Optimized data types and memory usage
- Batch processing for large datasets
- Smart chunking for memory management
"""

import os
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import lru_cache
import pickle
import hashlib
from pathlib import Path
import gc

from .config_optimized import (
    E_path, ENABLE_CACHING, CACHE_SIZE, CACHE_DIRECTORY, 
    USE_VECTORIZED_OPERATIONS, OPTIMIZE_DATA_TYPES, 
    BATCH_SIZE, MAX_WORKERS, CHUNK_SIZE
)
from .performance_monitor import performance_monitor, timed_operation, memory_monitor

class DataCache:
    """Intelligent caching system for data processing operations."""
    
    def __init__(self, cache_dir=CACHE_DIRECTORY):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.memory_cache = {}
        
    def _get_cache_key(self, *args, **kwargs):
        """Generate cache key from arguments."""
        key_str = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, key):
        """Get item from cache."""
        # Try memory cache first
        if key in self.memory_cache:
            performance_monitor.increment_counter('cache_hits')
            return self.memory_cache[key]
        
        # Try disk cache
        cache_file = self.cache_dir / f"{key}.pkl"
        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    data = pickle.load(f)
                self.memory_cache[key] = data  # Store in memory for faster access
                performance_monitor.increment_counter('cache_hits')
                return data
            except Exception:
                pass
        
        performance_monitor.increment_counter('cache_misses')
        return None
    
    def set(self, key, value):
        """Set item in cache."""
        self.memory_cache[key] = value
        
        # Also save to disk if enabled
        if ENABLE_CACHING:
            cache_file = self.cache_dir / f"{key}.pkl"
            try:
                with open(cache_file, 'wb') as f:
                    pickle.dump(value, f)
            except Exception:
                pass

# Global cache instance
data_cache = DataCache()

@timed_operation("create_pilot_df_optimized")
def create_pilot_df_optimized(activity_id, retailer):
    """
    Optimized version of create_pilot_df with caching and vectorized operations.
    """
    cache_key = data_cache._get_cache_key('pilot_df', activity_id, retailer)
    cached_result = data_cache.get(cache_key)
    if cached_result is not None:
        return cached_result
    
    with memory_monitor(f"create_pilot_df_{activity_id}_{retailer}"):
        # Load test list with optimized data types
        test_list = pd.read_excel(
            E_path + "\\Test Store List.xlsx",
            dtype={'Campaign_ID': 'int32', 'Store_Id': 'int64'}
        ).query("Campaign_ID == @activity_id")
        
        test_list['TestvControl'] = "Test"
        
        # Load store list with optimized reading
        store_list = pd.read_excel(
            E_path + "\\Store Codes" + f"\\{retailer}.xlsx",
            dtype={'Store code applied by the retailer': 'int64'}
        )
        
        # Vectorized operation for finding control stores
        test_store_ids = set(test_list['Store_Id'])
        control_mask = ~store_list['Store code applied by the retailer'].isin(test_store_ids)
        control_list = store_list[control_mask].copy()
        
        control_list['TestvControl'] = "Control"
        control_list = control_list.rename(columns={'Store code applied by the retailer': 'Store_Id'})
        control_list.insert(0, 'Campaign_ID', activity_id)
        
        print(control_list.head())
        
        # Efficient concatenation
        pilot_df = pd.concat([test_list, control_list], ignore_index=True, copy=False)
        print(pilot_df['TestvControl'].unique())
        
        # Optimize data types
        if OPTIMIZE_DATA_TYPES:
            pilot_df = optimize_dataframe_dtypes(pilot_df)
        
        pilot_df.to_excel(E_path + "\\pilot_df.xlsx", index=False)
        
        # Cache the result
        data_cache.set(cache_key, pilot_df)
        
        return pilot_df

@lru_cache(maxsize=CACHE_SIZE)
def get_sku_list_cached(retailer, brand):
    """
    Cached version of get_sku_list with optimized loading.
    """
    sku_list = pd.read_csv(
        E_path + "\\SKU List.csv",
        dtype={'ITEM CODE': 'int64', 'Retailer': 'category', 'BRAND': 'category'}
    ).query("Retailer == @retailer and BRAND == @brand")
    
    return sku_list

@timed_operation("create_val_data_optimized")
def create_val_data_optimized(sku, retailer):
    """
    Optimized version of create_val_data with better error handling and data types.
    """
    cache_key = data_cache._get_cache_key('val_data', sku, retailer)
    cached_result = data_cache.get(cache_key)
    if cached_result is not None:
        return cached_result
    
    val_path = os.path.join(E_path, 'SKU DATA', f'{retailer}')
    file_path = val_path + f"\\{sku}.csv"
    
    # Check if file exists before attempting to read
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"SKU file not found: {file_path}")
    
    # Optimized CSV reading with data types
    dtype_dict = {
        'Store_Code': 'int64',
        'Sales_Value': 'float32',
        'Sales_Units': 'float32'
    }
    
    store_df = pd.read_csv(file_path, dtype=dtype_dict, parse_dates=['WEEK'])
    print("Successful read")
    
    # Vectorized column renaming
    column_mapping = {
        'Store_Code': 'POC SAPID',
        'Store Code applied by the retailer': 'Store_Code',
        'Sales_Value': 'Sum(Value)',
        'Sales_Units': 'Sales_Units',
        'WEEK': 'Date'
    }
    
    store_df = store_df.rename(columns=column_mapping)
    
    # Optimized date operations
    if 'Date' in store_df.columns:
        store_df['Date'] = pd.to_datetime(store_df['Date'], errors='coerce')
        store_df['Year'] = store_df['Date'].dt.year.astype('int16')
        store_df['Month'] = store_df['Date'].dt.month.astype('int8')
        store_df['Period_Date'] = pd.to_datetime(store_df[['Year', 'Month']].assign(Day=1))
    
    # Cache the result
    data_cache.set(cache_key, store_df)
    
    return store_df

@timed_operation("mod1_optimized")
def mod1_optimized(store_df):
    """
    Optimized version of mod1 with vectorized operations and memory efficiency.
    """
    with memory_monitor("mod1_processing"):
        data = store_df.copy()
        
        # Vectorized string operations
        data['POC SAPID'] = data['POC SAPID'].astype(str)
        
        # More efficient concatenation
        data = pd.concat([data, data], ignore_index=True, copy=False)
        
        # Vectorized column name cleaning
        data.columns = [x.replace(' ', '_') for x in data.columns]
        
        # Vectorized value cleaning
        data['Sum(Value)'] = np.maximum(data['Sum(Value)'], 0)
        
        # Optimized date operations
        data['Date'] = pd.to_datetime(data['Date'], dayfirst=False)
        data['Year'] = data['Date'].dt.year
        data['Month'] = data['Date'].dt.month.astype(str).str.zfill(2)
        
        # Efficient groupby operation
        grouped_data = data.groupby(["POC_SAPID", "Month", "Date"], as_index=False).agg({
            "Sum(Value)": "sum"
        })
        
        # Optimized pivot operation
        molten_data = pd.melt(
            grouped_data, 
            id_vars=['POC_SAPID', 'Month', 'Date'], 
            value_vars=['Sum(Value)']
        ).sort_values(by=['POC_SAPID', 'Month', 'Date'])
        
        molten_data['MY'] = molten_data['Date'].astype(str)
        
        # Memory-efficient pivot
        Module1_data = molten_data.pivot_table(
            index='POC_SAPID', 
            columns='MY', 
            values='value',
            aggfunc='first'
        ).reset_index()
        
        # Optimize data types
        if OPTIMIZE_DATA_TYPES:
            Module1_data = optimize_dataframe_dtypes(Module1_data)
        
        return Module1_data

@timed_operation("read_data_optimized")
def read_data_optimized(Base_data, meta):
    """
    Optimized version of read_data with better memory management.
    """
    with memory_monitor("read_data_processing"):
        DATA = Base_data.copy()
        
        # Vectorized column name cleaning
        DATA.columns = [x.replace(' ', '_') for x in DATA.columns]
        DATA = DATA.rename(columns={'POC_SAPID': 'POC_ID'})
        
        # Efficient data cleaning
        DATA = DATA.dropna(subset=['POC_ID'])
        DATA['POC_ID'] = pd.to_numeric(DATA['POC_ID'], errors='coerce').astype('int64').astype(str)
        
        META = meta.copy()
        META = META.rename(columns={
            'Store number': 'POC_ID',
            'START DATE': 'Start_Date',
            'END DATE': 'End_Date',
            'TestvControl': 'Test_Control'
        })
        
        # Efficient duplicate removal
        META = META.drop_duplicates(subset=['POC_ID', 'Start_Date', 'End_Date'])
        
        print("Hi")
        print(META['Test_Control'].unique())
        
        # Optimized date parsing
        META['Start_Date'] = pd.to_datetime(META['Start_Date'], dayfirst=True)
        META['End_Date'] = pd.to_datetime(META['End_Date'], dayfirst=True)
        
        # Vectorized column cleaning
        META.columns = [x.replace(' ', '_') for x in META.columns]
        META = META.dropna(subset=['POC_ID'])
        META['POC_ID'] = pd.to_numeric(META['POC_ID'], errors='coerce').astype('int64').astype(str)
        
        try:
            # Efficient merge operation
            DATA = pd.merge(
                DATA, 
                META[['POC_ID', 'Start_Date', 'End_Date', 'Test_Control']], 
                on=['POC_ID'], 
                how='right'
            )
        except Exception as e:
            print("Error in mapping Activation Date to Volume-Data", e)
        
        return [DATA, META]

def optimize_dataframe_dtypes(df):
    """
    Optimize DataFrame data types for memory efficiency.
    """
    if not OPTIMIZE_DATA_TYPES:
        return df
    
    optimized_df = df.copy()
    
    for col in optimized_df.columns:
        col_type = optimized_df[col].dtype
        
        if col_type == 'object':
            # Try to convert to category if it has few unique values
            unique_ratio = len(optimized_df[col].unique()) / len(optimized_df)
            if unique_ratio < 0.5:
                optimized_df[col] = optimized_df[col].astype('category')
        
        elif col_type == 'int64':
            # Downcast integers
            col_min = optimized_df[col].min()
            col_max = optimized_df[col].max()
            
            if col_min >= 0:
                if col_max < 255:
                    optimized_df[col] = optimized_df[col].astype('uint8')
                elif col_max < 65535:
                    optimized_df[col] = optimized_df[col].astype('uint16')
                elif col_max < 4294967295:
                    optimized_df[col] = optimized_df[col].astype('uint32')
            else:
                if col_min > -128 and col_max < 127:
                    optimized_df[col] = optimized_df[col].astype('int8')
                elif col_min > -32768 and col_max < 32767:
                    optimized_df[col] = optimized_df[col].astype('int16')
                elif col_min > -2147483648 and col_max < 2147483647:
                    optimized_df[col] = optimized_df[col].astype('int32')
        
        elif col_type == 'float64':
            # Downcast floats
            optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='float')
    
    return optimized_df

def parallel_data_loading(sku_list, retailer, max_workers=None):
    """
    Load multiple SKU data files in parallel.
    """
    if max_workers is None:
        max_workers = min(MAX_WORKERS, len(sku_list))
    
    results = {}
    failed_skus = []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_sku = {
            executor.submit(create_val_data_optimized, sku, retailer): sku 
            for sku in sku_list
        }
        
        # Collect results
        for future in as_completed(future_to_sku):
            sku = future_to_sku[future]
            try:
                result = future.result()
                results[sku] = result
            except Exception as e:
                failed_skus.append((sku, str(e)))
    
    return results, failed_skus

def batch_process_data(data_list, process_func, batch_size=BATCH_SIZE):
    """
    Process data in batches to manage memory usage.
    """
    results = []
    
    for i in range(0, len(data_list), batch_size):
        batch = data_list[i:i + batch_size]
        batch_results = [process_func(item) for item in batch]
        results.extend(batch_results)
        
        # Force garbage collection after each batch
        if i % (batch_size * 2) == 0:
            performance_monitor.force_garbage_collection()
    
    return results
