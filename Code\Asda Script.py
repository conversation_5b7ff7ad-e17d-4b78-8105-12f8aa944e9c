import os
import pandas as pd
import glob

# Get folder path from user
folder_path = r'C:\Users\<USER>\Anheuser-Busch InBev\Accelerate 2.0 - Documents\Sainsbury'
year=[2023,2024,2025]

for i in year:
    file_pattern = folder_path+f"\\{i}"+"\\*.xlsx"
    file_list = sorted(glob.glob(file_pattern))
    all_data = []
    for file in file_list:
        # week = os.path.splitext(os.path.basename(file))[0].split('_')[-1]
        try:
            df = pd.read_excel(file, skiprows=7, usecols=range(5), dtype=str,engine="openpyxl")
            # Drop fully empty rows
            df = df.dropna(how='all')
            # Remove any repeated header rows in the data
            df = df[df.iloc[:, 0] != 'Periods']
            df['Week_Ending'] = pd.to_datetime(df['Periods'].str.extract(r'w/e\s+(\d{2}/\d{2}/\d{2})')[0], dayfirst=True)
            df['Week_Ending'] = df['Week_Ending'].dt.strftime('%m/%d/%y')
            # Add week column
            # df['WEEK'] = week
            all_data.append(df)
            print(f"Processed: {os.path.basename(file)}")
        except Exception as e:
            print(f"Could not process {file}: {e}")

if all_data:
    combined_df = pd.concat(all_data, ignore_index=True)
    # Rename columns for readability
    combined_df.columns = [
        'Periods',        # e.g., "1 w/e 06/01/24"
        'Item_Code',      # e.g., "2000000156844"
        'Store_Code',     # e.g., "4002"
        'Sales_Units',    # e.g., "0"
        'Sales_Value',    # e.g., "0"
        'WEEK'            # extracted from file name
    ]
    # Save as CSV in the same folder
    output_csv = os.path.join(r"C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Desktop\Prateek\CATMAN TM\Projects\UK Accelerate", "Sainsbury.csv")
    combined_df.to_csv(output_csv, index=False)
    print(f"\nAll done! Master CSV saved as: {output_csv}")
else:
    print("No data found to combine!")