import pandas as pd
import os
from config import Config

def create_pilot_df(activity_id, retailer):
    """Create pilot dataframe with test and control stores"""
    config = Config()
    test_list = pd.read_excel(os.path.join(config.BASE_PATH, config.TEST_STORE_FILE)).query("Campaign_ID == @activity_id")
    test_list['TestvControl'] = "Test"
    store_list = pd.read_excel(os.path.join(config.BASE_PATH, "Store Codes", f"{retailer}.xlsx"))
    control_list = store_list[~store_list['Store code applied by the retailer'].isin(test_list['Store_Id'])]
    control_list['TestvControl'] = "Control"
    control_list = control_list.rename(columns={'Store code applied by the retailer': 'Store_Id'})
    control_list.insert(0, 'Campaign_ID', activity_id)
    print(control_list.head())
    pilot_df = pd.concat([test_list, control_list], ignore_index=True)
    print(pilot_df['TestvControl'].unique())
    pilot_df.to_excel(os.path.join(config.BASE_PATH, "pilot_df.xlsx"))
    return pilot_df

def get_sku_list(retailer, brand):
    """Get SKU list for given retailer and brand"""
    config = Config()
    sku_list = pd.read_csv(os.path.join(config.BASE_PATH, config.SKU_LIST_FILE)).query("Retailer == @retailer and BRAND == @brand")
    return sku_list

def create_val_data(sku, retailer):
    """Create validation data for given SKU and retailer"""
    config = Config()
    val_path = os.path.join(config.BASE_PATH, 'SKU DATA', f'{retailer}')
    store_df = pd.read_csv(os.path.join(val_path, f"{sku}.csv"))
    print("Successful read")
    store_df.rename(columns={
        'Store_Code': 'POC SAPID',
        'Store Code applied by the retailer': 'Store_Code',
        'Sales_Value': 'Sum(Value)',
        'Sales_Units': 'Sales_Units',
        'Week_Ending': 'Date'
    }, inplace=True)
    store_df['Date'] = pd.to_datetime(store_df['Date'], errors='coerce')
    store_df['Year'] = store_df['Date'].dt.year
    store_df['Month'] = store_df['Date'].dt.month
    store_df['Period_Date'] = pd.to_datetime(store_df[['Year', 'Month']].assign(Day=1))
    return store_df