"""
Main orchestrator module for the Accelerate analysis pipeline.
Contains the main accelerate function and execution logic.
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from operator import add

from .config import (
    E_path, final_result_output, RESTRICT_BASELINE_TO, THRESHOLD_PERCENTAGE,
    MAX_POC_COUNT, MIN_POC_COUNT, CONTROL_LIMIT, get_activity_df, ensure_directories
)
from .data_processing import create_pilot_df, get_sku_list, create_val_data, mod1, read_data
from .utils import (
    find_date, get_data_indices_n_years, get_activation_week_index, get_end_week_index,
    remove_nulls_by_threshold_in_range, replace_nulls_with_0
)
from .analysis import (
    testvscontrolfcn, get_dist_mat_grp, get_optimal_n_cluster, get_clustered_data,
    filter_control_pocs, get_uplift, get_uplift_val, lift_outlier_iqr, significance_level
)


def accelerate(start_date, end_date, brand, activity_id, campaign, retailer):
    """
    Main accelerate function that orchestrates the entire analysis pipeline.
    
    Args:
        start_date: Campaign start date
        end_date: Campaign end date
        brand: Brand name
        activity_id: Activity/Campaign ID
        campaign: Campaign name
        retailer: Retailer name
    """
    global final_result_output
    print(campaign, activity_id, retailer)
    
    # Ensure required directories exist
    ensure_directories()
    
    # Create metadata
    target_path = os.path.join(E_path, 'META')
    if not os.path.isdir(target_path):
        os.makedirs(target_path, exist_ok=True)
        
    meta = create_pilot_df(activity_id, retailer)
    meta['START DATE'] = start_date
    meta['END DATE'] = end_date
    meta.rename(columns={'Store_Id': 'POC_ID'}, inplace=True)
    meta.to_csv(target_path + f"\\Meta_{activity_id}.csv")
    
    # Get SKU list for processing
    sku_list = get_sku_list(retailer, brand)
    
    # Process each SKU
    for sku in sku_list["ITEM CODE"]:
        print("sku :", sku)
        try:
            # Load and process data
            store_df = create_val_data(sku, retailer)
            Base_data = mod1(store_df)
            Base_data['POC_SAPID'] = pd.to_numeric(Base_data['POC_SAPID'], errors='coerce')
            Base_data = Base_data.dropna(subset=['POC_SAPID'])
            Base_data['POC_SAPID'] = Base_data['POC_SAPID'].astype('int')
            
            DATA, META = read_data(Base_data, meta)
            print(DATA['Test_Control'].unique())  
            
            # Replace zeros with NaN for processing
            DATA.replace(0, np.nan, inplace=True)
            Test = DATA[DATA['Test_Control'] == 'Test']
            Control = DATA[DATA['Test_Control'] == 'Control']
            
            # Process dates
            META["Start_Date"] = pd.to_datetime(META["Start_Date"])
            META["End_Date"] = pd.to_datetime(META["End_Date"])
            test_start_min_date = META["Start_Date"].min()
            test_end_max_date = META["End_Date"].max()
            min_column, max_column = find_date(test_start_min_date, test_end_max_date)
            
            # Prepare data for analysis
            Post_Analysis_DATA = pd.concat([DATA[["POC_ID"]], DATA.iloc[:, DATA.columns.get_loc(max_column) + 1:DATA.shape[1]]], axis=1)
            Post_Analysis_DATA = Post_Analysis_DATA.loc[:, ~Post_Analysis_DATA.columns.duplicated()]
            
            if DATA.columns.get_loc(min_column) - RESTRICT_BASELINE_TO < 0:
                start = 0
            else:
                start = DATA.columns.get_loc(min_column) - RESTRICT_BASELINE_TO
                
            DATA = pd.concat([DATA[["POC_ID", "Start_Date", 'End_Date', 'Test_Control']], DATA.iloc[:, start:DATA.columns.get_loc(max_column) + 1]], axis=1)
            DATA = DATA.loc[:, ~DATA.columns.duplicated()]
            
            mid_index = DATA.columns.get_loc(min_column)
            min_index, max_index, min_year, max_year = get_data_indices_n_years(list(Post_Analysis_DATA.columns))
            columns = list(Post_Analysis_DATA)
            Post_Analysis_DATA = replace_nulls_with_0(Post_Analysis_DATA, min_index, max_index, columns)
            
            min_index, max_index, min_year, max_year = get_data_indices_n_years(list(DATA.columns))
            columns = list(DATA)
            threshold = THRESHOLD_PERCENTAGE
            DATA = remove_nulls_by_threshold_in_range(DATA, threshold, min_index, mid_index)
            DATA = replace_nulls_with_0(DATA, min_index, max_index, columns)
            
            # Filter data
            Test = DATA[DATA['Test_Control'] == 'Test']
            Control = DATA[DATA['Test_Control'] == 'Control']
            controlled = DATA[DATA['Test_Control'] == 'Control']
            test = DATA[DATA['Test_Control'] == 'Test']
            
            DATA['NET_AVG_Y1'] = DATA.iloc[:, min_index:mid_index].T.mean()
            DATA = DATA[DATA['NET_AVG_Y1'] != 0]
            controlled = DATA[DATA['Test_Control'] == 'Control']
            test = DATA[DATA['Test_Control'] == 'Test']
            
            # Create backup and process
            DATA_backup = DATA.copy()
            mid_index = DATA_backup.columns.get_loc(min_column)
            DATA_backup["Baseline Period Avg"] = DATA_backup.iloc[:, min_index:mid_index].T.mean()
            DATA_backup["Promo Period Avg"] = DATA_backup.iloc[:, mid_index:-2].T.mean()
            
            controlled_backup = DATA_backup[DATA_backup['Test_Control'] == 'Control']
            test_backup = DATA_backup[DATA_backup['Test_Control'] == 'Test']
            controlled = controlled_backup
            test = test_backup
            
            controlled = controlled[controlled['Promo Period Avg'] != 0]
            controlled['dev'] = (controlled['Promo Period Avg'] - controlled['Baseline Period Avg']) / controlled['Baseline Period Avg']
            controlled = controlled[controlled['dev'] < 2]
            controlled.drop(['dev'], axis=1, inplace=True)
            
            # Generate test vs control comparison
            testvscontrol_before = testvscontrolfcn(controlled, test)
            
            # Create and save plot
            save_path = os.path.join(E_path, "Plots", f"{activity_id}.png")
            plt.rcParams['figure.figsize'] = [18, 8]
            plt.plot(testvscontrol_before.T, marker='o')
            plt.xticks(rotation=90)
            plt.legend()
            plt.savefig(save_path, bbox_inches="tight", dpi=300)  
            plt.close()
            
            # Continue with clustering and analysis...
            _process_clustering_and_analysis(DATA, controlled, test, min_index, max_index, mid_index, 
                                           META, activity_id, sku, campaign, retailer)
            
        except Exception as e:
            print(f"Error processing SKU {sku}: {e}")
            continue
    
    # Save final results
    final_result_output.to_excel(E_path + "\\Results" + f"\\Final_Results_{activity_id}.xlsx", sheet_name="Results", index=False)


def _process_clustering_and_analysis(DATA, controlled, test, min_index, max_index, mid_index, 
                                   META, activity_id, sku, campaign, retailer):
    """
    Helper function to process clustering and analysis part of the pipeline.
    """
    # Initialize variables for clustering
    percentile_grp = [0]
    percentile_values_old = []
    percentile_values = []
    data_vol_grp = []
    poc_ids_grp = []
    date_grp = []
    dm_data_grp = []
    dist_mat_grp = []
    num_clusters_grp = []
    kmeans_grp = []
    clustered_data_grp = []
    run_test_pocs = []
    run_ctrl_pocs = []
    
    # Prepare data for clustering
    data_live = pd.concat([controlled, test])
    data_live = data_live.sort_values(by=['NET_AVG_Y1'])
    test_live = test.sort_values(by=['NET_AVG_Y1'])
    data_live = data_live[data_live.NET_AVG_Y1 != 0]
    test_live = test_live[test_live.NET_AVG_Y1 != 0]
    
    for p in percentile_grp:
        _q = np.percentile(test_live.NET_AVG_Y1, p)
        percentile_values_old.append(_q)
    
    a = percentile_values_old
    t1 = (data_live['NET_AVG_Y1']).sort_values().reset_index().drop(columns='index')
    _l = -1
    _r = -1
    data_vol_grp = []
    
    for pv in percentile_values:
        _r = pv
        if _l == -1:
            data_vol_grp.append(data_live[data_live['NET_AVG_Y1'] < _r])
        else:
            data_vol_grp.append(data_live[(data_live['NET_AVG_Y1'] < _r) & (data_live['NET_AVG_Y1'] >= _l)])
        _l = _r
    
    data_vol_grp.append(data_live[data_live['NET_AVG_Y1'] >= _r])
    data_vol_grp_copy = data_vol_grp
    
    # Split large groups
    dm_data_grp_split = []
    max_poc_count = MAX_POC_COUNT
    min_poc_count = MIN_POC_COUNT
    
    for i in range(len(data_vol_grp_copy)):
        if (len(data_vol_grp_copy[i]) <= max_poc_count):
            dm_data_grp_split.append(data_vol_grp_copy[i])
        else:
            tot = int(np.ceil(len(data_vol_grp_copy[i]) / max_poc_count))
            min_row_range = 0
            for j in range(tot):
                max_row_range = min_row_range + max_poc_count
                if max_row_range > len(data_vol_grp_copy[i]):
                    max_row_range = len(data_vol_grp_copy[i])
                if (len(data_vol_grp_copy[i]) - max_row_range) < min_poc_count:
                    max_row_range = len(data_vol_grp_copy[i])
                    dm_data_grp_split.append(data_vol_grp_copy[i].iloc[min_row_range:max_row_range, :])
                    break
                dm_data_grp_split.append(data_vol_grp_copy[i].iloc[min_row_range:max_row_range, :])
                min_row_range = max_row_range
    
    dm_data_grp_split = data_vol_grp
    _t_upd = []
    vol_grp_cnt = 1
    
    for vg in dm_data_grp_split:
        _t = vg.reset_index(drop=True)
        _t_upd.append(_t)
    
    dm_data_grp_split = _t_upd
    
    # Extract POC IDs and dates
    poc_ids_grp = []
    for vg in dm_data_grp_split:
        _poc_id = vg['POC_ID']
        poc_ids_grp.append(_poc_id)
    
    date_grp = []
    for vg in dm_data_grp_split:
        _poc_id = vg['Start_Date']
        date_grp.append(_poc_id)
    
    # Prepare data for distance matrix calculation
    dm_data_grp = []
    for vg in dm_data_grp_split:
        _t = vg
        _t = _t[_t.columns[np.concatenate([range(min_index, max_index)])]]     
        _max_val = _t.iloc[:, :].T.max()
        _t.iloc[:, :] = _t.iloc[:, :].div(_max_val, axis=0)
        dm_data_grp.append(_t)
    
    percentile_values = []
    kmeans_grp = []
    
    # Calculate distance matrices and clustering
    dist_mat_grp = get_dist_mat_grp(dm_data_grp)
    num_clusters_grp = []    
    num_clusters_grp = get_optimal_n_cluster(dist_mat_grp)
    clustered_data_grp = [] 
    
    print(DATA['Test_Control'].unique())
    clustered_data_grp = get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, DATA)
    
    # Process test POCs
    total_test_pocs = DATA[DATA['Test_Control'] == 'Test'].POC_ID.tolist()
    print(len(total_test_pocs))
    
    data_len = len(clustered_data_grp)
    test_pocs_len = len(total_test_pocs)
    
    # Initialize results
    APT_RESULTS = META.copy()
    APT_RESULTS = APT_RESULTS[APT_RESULTS.POC_ID.isin(total_test_pocs)]
    APT_RESULTS["Start_Date"] = pd.to_datetime(APT_RESULTS["Start_Date"])
    APT_RESULTS["End_Date"] = pd.to_datetime(APT_RESULTS["End_Date"])
    APT_RESULTS.drop(['Start_Date', 'End_Date'], axis=1, inplace=True)
    
    # Set cluster for first group
    clustered_data_grp[0].Cluster = 0
    clustered_data_grp[0].to_excel(E_path + "\\Clustered_Data" + f"\\clustered_group_0_{activity_id}_{sku}.xlsx", index=False)
    
    # Process each test POC
    i = 0
    limit = CONTROL_LIMIT
    test_control_list = pd.DataFrame()
    
    for test_poc in total_test_pocs: 
        i = i + 1
        if (i % 100 == 0):
            print("Test POC No:", i)
            
        for di in range(0, data_len):
            g = clustered_data_grp[di]
            
            if (len(g[g.POC_ID == test_poc]) > 0):
                global_test = g[g.POC_ID == str(test_poc)]
                global_ctrl = g[g.Test_Control == 'Control']
                
                if len(global_ctrl) > 0:
                    ctrl_len_before_filter = len(global_ctrl)
                    global_test["Start_Date"] = pd.to_datetime(global_test["Start_Date"])
                    global_test["End_Date"] = pd.to_datetime(global_test["End_Date"])
                    activation_on = get_activation_week_index(global_test.Start_Date.iloc[0], list(global_test))
                    activation_end = get_end_week_index(global_test.End_Date.iloc[0], list(global_test))                
                    
                    if len(global_ctrl) > limit:
                        global_ctrl = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
                    
                    ctrl_len_after_filter = len(global_ctrl)
                    ctrl_outliers = ctrl_len_before_filter - ctrl_len_after_filter
                    
                    if ((activation_end - activation_on) + 1 > 12):
                        validation_period = 2
                    else:
                        validation_period = 1
                        
                    validation_start = activation_on - validation_period
                    validation_end = activation_on
                    
                    global_ctrl_test = global_ctrl
                    global_ctrl_test['Test_POC_ID'] = test_poc
                    control_list = global_ctrl_test[["Test_POC_ID", "POC_ID"]]
                    test_control_list = pd.concat([test_control_list, control_list])
                    
                    desc_test = global_test[global_test.columns[np.concatenate([range(min_index, max_index + 1)])]].mean()
                    desc_ctrl = global_ctrl[global_ctrl.columns[np.concatenate([range(min_index, max_index + 1)])]].mean()
                    
                    # Calculate uplift
                    perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol = get_uplift(
                        desc_test, desc_ctrl, min_index, activation_on, activation_end, 
                        global_test.POC_ID, global_ctrl.POC_ID, ctrl_outliers, 
                        RESTRICT_BASELINE_TO, APT_RESULTS, campaign, retailer, sku
                    )
                    
                    # Calculate validation lift
                    validation_lift = get_uplift_val(
                        desc_test, desc_ctrl, min_index, activation_on, validation_start, 
                        validation_end, global_test.POC_ID, global_ctrl.POC_ID, 
                        RESTRICT_BASELINE_TO, APT_RESULTS
                    )
    
    # Process final results
    col = ['Campaign_ID', 'Test_Control', 'Start_Date', 'End_Date']
    print("XYZ")
    print(META.columns.tolist())
    temp = META.drop(col, axis=1)
    print("dropped")
    
    try:
        test_control_list = test_control_list.rename(columns={'POC_ID': 'Control_POC_ID'})
        test_control_list_temp = test_control_list.merge(temp, left_on='Test_POC_ID', right_on='POC_ID', how='left')
        test_control_list_temp = test_control_list_temp.rename(columns={
            'GEOGRAPHY_DESCRIPTION': 'Test_GEOGRAPHY_DESCRIPTION',
            'STORE_NO': 'Test_Store_No'
        })
        test_control_list_final = test_control_list_temp.merge(temp, left_on='Control_POC_ID', right_on='POC_ID', how='left')
        test_control_list_final = test_control_list_final.rename(columns={
            'GEOGRAPHY_DESCRIPTION_y': 'Control_GEOGRAPHY_DESCRIPTION',
            'STORE_NO': 'Control_Store_No'
        })
        test_control_list_final = test_control_list_final.drop(['POC_ID_x', 'POC_ID_y'], axis=1)
    except:
        pass
    
    print("next_sig")
    APT_Outlier = lift_outlier_iqr(APT_RESULTS)
    sig_level = significance_level(APT_Outlier, APT_RESULTS, sku)
    print("Sig Level calculated")
    
    # Update global results
    global final_result_output
    final_result_output = pd.concat([final_result_output, APT_RESULTS], ignore_index=True)
    test_control_list.to_excel(E_path + "\\TestvCtrl" + f"\\{activity_id}.xlsx", sheet_name="Control_Mapping", index=False)


def run_accelerate_pipeline():
    """
    Main function to run the entire accelerate pipeline for all activities.
    """
    global final_result_output
    
    # Load activity data
    activity_df = get_activity_df()
    
    # Process each activity
    for _, row in activity_df.iterrows():
        accelerate(row['Nielsen Start'], row['Nielsen End'], row['Brand'], 
                  row['Activity UID'], row['Campaign'], row['Retailer'])
    
    # Save final consolidated results
    final_result_output.to_excel(E_path + "\\Results" + "\\Final_Results.xlsx", sheet_name="Results", index=False)
    
    print("The End")
    current_datetime = datetime.now()
    formatted_timestamp = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
    print(f"Formatted Timestamp: {formatted_timestamp}")


if __name__ == "__main__":
    run_accelerate_pipeline()
