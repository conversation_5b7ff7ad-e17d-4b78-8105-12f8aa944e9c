import os
import pandas as pd
import matplotlib.pyplot as plt

def generate_test_control_plot(testvscontrol_data, activity_id, save_path):
    plt.rcParams['figure.figsize'] = [18, 8]
    plt.plot(testvscontrol_data.T, marker='o')
    plt.xticks(rotation=90)
    plt.legend()
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, bbox_inches="tight", dpi=300)
    plt.close()

def export_results_to_excel(final_results, activity_id, base_path):
    output_dir = os.path.join(base_path, "Results")
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, f"Final_Results_{activity_id}.xlsx")
    final_results.to_excel(output_path, sheet_name="Results", index=False)

def export_control_mapping(test_control_list, activity_id, base_path):
    output_dir = os.path.join(base_path, "TestvCtrl")
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, f"{activity_id}.xlsx")
    test_control_list.to_excel(output_path, sheet_name="Control_Mapping", index=False)

def create_output_directories(base_path):
    dirs = ["META", "Plots", "Results", "TestvCtrl", "Clustered_Data"]
    for dir_name in dirs:
        os.makedirs(os.path.join(base_path, dir_name), exist_ok=True)