import os
import pandas as pd
import glob

# Get folder path from user
folder_path = input("Enter the folder path containing your Excel files: ").strip()

# Optional: pattern for file names, e.g., 'Sales_ITEM_STORE_Asda_*.xlsx'
file_pattern = os.path.join(folder_path, "*.xlsx")
file_list = sorted(glob.glob(file_pattern))

all_data = []

for file in file_list:
    # Extract week from file name (assumes last part before .xlsx is week)
    week = os.path.splitext(os.path.basename(file))[0].split('_')[-1]
    try:
        # Read data, skipping first 7 rows, reading only first 5 columns
        df = pd.read_excel(file, skiprows=7, usecols=range(5), dtype=str)
        # Drop fully empty rows
        df = df.dropna(how='all')
        # Remove any repeated header rows in the data
        df = df[df.iloc[:, 0] != 'Periods']
        # Add week column
        df['WEEK'] = week
        all_data.append(df)
        print(f"Processed: {os.path.basename(file)}")
    except Exception as e:
        print(f"Could not process {file}: {e}")

# Combine all data
if all_data:
    combined_df = pd.concat(all_data, ignore_index=True)
    # Rename columns for readability
    combined_df.columns = [
        'Periods',        # e.g., "1 w/e 06/01/24"
        'Item_Code',      # e.g., "2000000156844"
        'Store_Code',     # e.g., "4002"
        'Sales_Units',    # e.g., "0"
        'Sales_Value',    # e.g., "0"
        'WEEK'            # extracted from file name
    ]
    # Save as CSV in the same folder
    output_csv = os.path.join(folder_path, "Combined_Sales_Asda_Master.csv")
    combined_df.to_csv(output_csv, index=False)
    print(f"\nAll done! Master CSV saved as: {output_csv}")
else:
    print("No data found to combine!")
