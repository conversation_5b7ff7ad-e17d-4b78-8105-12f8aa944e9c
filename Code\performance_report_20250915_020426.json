{"execution_summary": {"total_execution_time": 1.0224709510803223, "peak_memory_usage_gb": 15.3719482421875, "total_operations": 6, "operations_per_second": 5.868137372177196, "garbage_collections": 0}, "timing_breakdown": {"test_operation": {"total_time": 0.1151120662689209, "average_time": 0.1151120662689209, "min_time": 0.1151120662689209, "max_time": 0.1151120662689209, "count": 1, "std_dev": 0.0}, "data_validation": {"total_time": 0.03518342971801758, "average_time": 0.03518342971801758, "min_time": 0.03518342971801758, "max_time": 0.03518342971801758, "count": 1, "std_dev": 0.0}, "outlier_detection": {"total_time": 0.06285572052001953, "average_time": 0.031427860260009766, "min_time": 0.0287933349609375, "max_time": 0.03406238555908203, "count": 2, "std_dev": 0.0026345252990722656}, "data_preprocessing": {"total_time": 0.09846806526184082, "average_time": 0.09846806526184082, "min_time": 0.09846806526184082, "max_time": 0.09846806526184082, "count": 1, "std_dev": 0.0}}, "memory_analysis": {"peak_memory_gb": 15.3719482421875, "average_memory_gb": 15.366828918457031, "min_memory_gb": 15.361709594726562, "memory_variance": 2.620747545734048e-05, "memory_trend": "increasing"}, "resource_utilization": {"average_cpu_percent": 0.0, "peak_cpu_percent": 0.0, "cpu_utilization_efficiency": "low"}, "bottlenecks": [], "recommendations": ["High memory usage detected - consider processing data in smaller chunks", "Low CPU utilization - consider increasing parallel processing"]}