"""
Configuration module for Accelerate analysis pipeline.
Contains global variables, paths, and configuration settings.
"""

import os
import pandas as pd
import numpy as np
import warnings

# Suppress warnings
warnings.filterwarnings('ignore')

# Set pandas display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
pd.set_option('display.float_format', '{:.2f}'.format)

# Global paths
E_path = r"C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Accelerate Improved"

# Global variables
final_result_output = pd.DataFrame()

# Configuration constants
RESTRICT_BASELINE_TO = 12
THRESHOLD_PERCENTAGE = 70
MAX_POC_COUNT = 2000
MIN_POC_COUNT = 200
CONTROL_LIMIT = 5
UPPER_LIFT_BOUND = 300
LOWER_LIFT_BOUND = -300
ALPHA_SIGNIFICANCE = 0.05

# File paths
ACTIVITY_FILE = "Activity_ID_List.xlsx"
TEST_STORE_FILE = "Test Store List.xlsx"
SKU_LIST_FILE = "SKU List.csv"

def get_activity_df():
    """Load activity dataframe from Excel file."""
    return pd.read_excel(os.path.join(E_path, ACTIVITY_FILE))

def ensure_directories():
    """Ensure all required directories exist."""
    directories = [
        os.path.join(E_path, 'META'),
        os.path.join(E_path, 'Plots'),
        os.path.join(E_path, 'Clustered_Data'),
        os.path.join(E_path, 'TestvCtrl'),
        os.path.join(E_path, 'Results')
    ]
    
    for directory in directories:
        if not os.path.isdir(directory):
            os.makedirs(directory, exist_ok=True)
