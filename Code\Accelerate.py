import os
import pandas as pd
from scipy.stats import wilcoxon
import pandas as pd
import glob
import numpy as np
import os
import calendar
from termcolor import colored
import datetime
import sys
import re
import warnings
from sklearn.linear_model import LinearRegression
from scipy.stats import ttest_ind
import matplotlib.pyplot as plt
from dtaidistance import dtw
from dtaidistance import clustering
from scipy import stats
import pickle
import sklearn.metrics
import math
import warnings
from scipy.stats import wilcoxon
from scipy.stats import ttest_rel
from datetime import datetime
from scipy.stats import f_oneway
from pandas import Series
import matplotlib.pyplot as plt
import numpy as np
from numpy import inf
import time
from sklearn.metrics import silhouette_score
from sklearn.cluster import KMeans
pd.set_option('display.max_columns',None)
pd.set_option('display.max_rows',None)
pd.set_option('display.float_format','{:.2f}'.format)
warnings.filterwarnings('ignore')

global path
E_path=r"C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Accelerate Improved"
final_result_output=pd.DataFrame()
# activity_df = pd.read_excel(E_path+"\\Activity ID List.xlsx")
activity_df = pd.read_excel(E_path+"\\Activity_ID_List.xlsx")

def create_pilot_df(activity_id,retailer):
    test_list = pd.read_excel(E_path+"\\Test Store List.xlsx").query("Campaign_ID == @activity_id")
    test_list['TestvControl']="Test"
    store_list = pd.read_excel(E_path+"\\Store Codes"+f"\\{retailer}.xlsx")
    control_list = store_list[~store_list['Store code applied by the retailer'].isin(test_list['Store_Id'])]
    control_list['TestvControl'] = "Control"
    control_list = control_list.rename(columns={'Store code applied by the retailer':'Store_Id'})
    control_list.insert(0,'Campaign_ID',activity_id)
    print(control_list.head())
    pilot_df = pd.concat([test_list, control_list], ignore_index=True)
    print(pilot_df['TestvControl'].unique())
    pilot_df.to_excel(E_path+"\\pilot_df.xlsx")
    return pilot_df

def get_sku_list(retailer,brand):
    sku_list = pd.read_csv(E_path+"\\SKU List.csv").query("Retailer == @retailer and BRAND == @brand")
    return sku_list

def create_val_data(sku,retailer):
    val_path = os.path.join(E_path,'SKU DATA',f'{retailer}')
    # print(val_path)
    store_df = pd.read_csv(val_path+f"\\{sku}.csv")
    print("Successful read")
    store_df.rename(columns={
    'Store_Code': 'POC SAPID',
    'Store Code applied by the retailer': 'Store_Code',
    'Sales_Value': 'Sum(Value)',
    'Sales_Units': 'Sales_Units',
    'WEEK':'Date'
    }, inplace=True)
    store_df['Date'] = pd.to_datetime(store_df['Date'], errors='coerce')
    store_df['Year'] = store_df['Date'].dt.year
    store_df['Month'] = store_df['Date'].dt.month
    store_df['Period_Date'] = pd.to_datetime(store_df[['Year', 'Month']].assign(Day=1))
    return store_df

def mod1(store_df):
    data= store_df
    data['POC SAPID']=data['POC SAPID'].astype(str)
    data = pd.concat([data, data],ignore_index=True)
    list_finaldata = list(data)
    mod_cols = [x.replace(' ', '_') for x in list_finaldata]
    data.columns = mod_cols
    data['Sum(Value)'] = data['Sum(Value)'].apply(lambda x: x if x>=0 else 0)
    data['Date'] = pd.to_datetime(data['Date'], dayfirst=False)
    data['Year'] = pd.DatetimeIndex(data['Date']).year
    data['Month'] = pd.DatetimeIndex(data['Date']).month
    data['Month'] = data['Month'].astype('str')
    data['Month']= data.Month.str.pad(2,side='left',fillchar='0')
    data = data.groupby(["POC_SAPID","Month","Date"]).agg({"Sum(Value)":"sum"}).reset_index()
    data_groupby = ['POC_SAPID']
    ident_cols = ['POC_SAPID','Month','Date']
    exp_cols = ['POC_SAPID']
    val_cols = ['Sum(Value)']
    molten_data = pd.melt(data, id_vars=ident_cols, value_vars=val_cols).sort_values(by=['POC_SAPID','Month','Date'])
    molten_data['MY'] = molten_data['Date'].astype('str')
    molten_data = molten_data.sort_values(by=['POC_SAPID','Month','Date'])
    Module1_data = molten_data.pivot(index='POC_SAPID', columns='MY', values='value')
    Module1_data.reset_index(inplace=True)
    return Module1_data

def read_data(Base_data,meta): 
    DATA = Base_data    # From the Previous Module 
    nd_cols = [x.replace(' ', '_') for x in DATA.columns]
    DATA.columns = nd_cols
    # print(DATA.columns.tolist())
    DATA = DATA.rename(columns={'POC_SAPID':'POC_ID'})
    DATA = DATA.dropna(subset=['POC_ID'])
    DATA.POC_ID = DATA.POC_ID.astype(int)
    DATA.POC_ID = DATA.POC_ID.astype(str)
    META=meta
    
    META = META.rename(columns={'Store number':'POC_ID','START DATE':'Start_Date','END DATE':'End_Date','TestvControl':'Test_Control'})
    META = META.drop_duplicates(subset=['POC_ID', 'Start_Date', 'End_Date'])
    print("Hi")
    print(META['Test_Control'].unique())
    # META.to_excel(E_path+"\\Meta_Test.xlsx")
    META['Start_Date'] = pd.to_datetime(META['Start_Date'], dayfirst=True)
    META['End_Date'] = pd.to_datetime(META['End_Date'], dayfirst=True)        
    nd_cols = [x.replace(' ', '_') for x in META.columns]
    META.columns = nd_cols
    META = META.dropna(subset=['POC_ID'])
    META.POC_ID = META.POC_ID.astype(int)
    META.POC_ID = META.POC_ID.astype(str)
    
    try:
        DATA = pd.merge(DATA, META[['POC_ID','Start_Date','End_Date','Test_Control']], on=['POC_ID'], how='right') #,'End_Date'
        d_cols = list(DATA)
        DATA.columns = d_cols                
    except Exception as e:
        print("Error in mapping Activation Date to Volume-Data", e)        
    return [DATA,META]

def find_date(min_date,max_date):
    min_month=str(min_date.month).zfill(2)
    min_col=min_date
    min_col = min_date.strftime('%Y-%m-%d')
    max_month=str(max_date.month).zfill(2)
    max_col=max_date
    max_col = max_date.strftime('%Y-%m-%d')
    return(min_col,max_col)

def remove_nulls_by_threshold_in_range(data, threshold,min_index,data_end_index):
    data_sub = data.iloc[:,min_index:data_end_index].copy()
    _vol_thres = int(threshold*(data_end_index - min_index)/100)
    data_sub = data_sub.dropna(thresh = _vol_thres)
    return (data[data.index.isin(data_sub.index)])

def replace_nulls_with_0(data,min_index,end_index,columns):
    _vol_data_cols = [columns[x] for x in range(min_index,end_index+1)]
    data[_vol_data_cols] = data[_vol_data_cols].replace({np.nan:0})
    return data

def get_data_indices_n_years(columns):
    date_columns = []
    date_indices = {}
    for i, col in enumerate(columns):
        try:
            dt = datetime.strptime(col, '%Y-%m-%d')
            date_columns.append(dt)
            date_indices[dt] = i
        except:
            continue  # Skip non-date columns like POC_ID, Start_Date, etc.

    if not date_columns:
        raise ValueError("No valid date columns found in the format YYYY-MM-DD.")
    min_date = min(date_columns)
    max_date = max(date_columns)
    index_min = date_indices[min_date]
    index_max = date_indices[max_date]
    return index_min, index_max, min_date.year, max_date.year

def testvscontrolfcn(control_data, test_data):
    # Drop known non-numeric or irrelevant columns
    drop_columns = ['Start_Date', 'End_Date', 'NET_AVG_Y1', 'POC_ID']
    control_data = control_data.drop(columns=[col for col in drop_columns if col in control_data.columns])
    test_data = test_data.drop(columns=[col for col in drop_columns if col in test_data.columns])

    # Replace 0 with NaN
    control_data = control_data.replace(0, np.nan)
    test_data = test_data.replace(0, np.nan)

    # Select only numeric columns (to avoid TypeError)
    control_data = control_data.select_dtypes(include='number')
    test_data = test_data.select_dtypes(include='number')

    # Compute means
    test_mean = test_data.mean(axis=0, skipna=True).to_frame().transpose()
    control_mean = control_data.mean(axis=0, skipna=True).to_frame().transpose()

    # Align column order (optional but safe)
    control_mean = control_mean[test_mean.columns]

    # Combine test and control means
    test_control_mean = pd.concat([test_mean, control_mean], ignore_index=True)
    test_control_mean.rename(index={0: 'Test', 1: 'Control'}, inplace=True)

    return test_control_mean

def get_dist_mat_grp(dm_data_grp):
    vol_grp_cnt = 1
    dist_mat_grp = []
    dm_data_len = len(dm_data_grp)
    
    for dm_vg in dm_data_grp:
        try:
            t1 = time.time()
            np.random.seed(47)
            _dm = dtw.distance_matrix(dm_vg.values, parallel = False)

            _nan_val = np.isnan(_dm)
            _inf_val = np.isinf(_dm)

            _dm[_nan_val] = 0
            _dm[_inf_val] = 0

            dist_mat_grp.append(_dm)

            _disp_msg = "[DM] Took {} seconds [Volume-Group-"+str(vol_grp_cnt)+"]"
            print(_disp_msg.format(time.time() - t1))
            vol_grp_cnt = vol_grp_cnt + 1
        except Exception as e:
            print(e)
            print("Error in calculating distance matrix for Group-"+str(vol_grp_cnt))
            vol_grp_cnt = vol_grp_cnt + 1
            
    return dist_mat_grp

def get_optimal_n_cluster(dist_mat_grp):
    vol_grp_cnt = 1
    num_clusters_grp = []
    dist_mat_len = len(dist_mat_grp)

    for _di in range(0,dist_mat_len):
        t1 = time.time()
        max_sil_score = -1
        opt_clus = 1

        X = dist_mat_grp[_di]
        #decide min/max no of clusters
        for n_cluster in range(1, 3):
            try:
                kmeans = KMeans(n_clusters=n_cluster,random_state=47).fit(X)
                label = kmeans.labels_
                sil_coeff = silhouette_score(X, label, metric='euclidean')

                if sil_coeff > max_sil_score:
                    max_sil_score = sil_coeff
                    opt_clus = n_cluster

                print("\tFor n_clusters={}, The Silhouette Coefficient is {}".format(n_cluster, sil_coeff))
            except:
                print("\tError in finding optimal cluster for Group-"+str(1+_di))

        #num_clusters_grp.append(opt_clus+1)
        num_clusters_grp.append(opt_clus)

        _disp_msg = "[Volume-Group-"+str(vol_grp_cnt)+"] | Optimal n-Cluster = "+str(opt_clus)+" | Took {} seconds"
        print(_disp_msg.format(time.time() - t1))
        print("---------------------------------------------------------------------------")
        vol_grp_cnt = vol_grp_cnt + 1
    
    return num_clusters_grp

def get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp,kmeans_grp,DATA):
    index = 0
    vol_grp_cnt = 1
    clustered_data_grp = []
    for _dm in dist_mat_grp:
        t1 = time.time()
        kmeans = KMeans(n_clusters=num_clusters_grp[index], random_state=47).fit(_dm)
        kmeans_grp.append(kmeans)
        _labels = kmeans.predict(_dm)
        
        """
        _clus_info = pd.DataFrame(_labels)
        
        _clus_info.insert(0, "DATE", 0, True)
        _clus_info.insert(0, "POC_ID", 0, True)
        _clus_info['DATE'] = date_grp[index].tolist()
        _clus_info['POC_ID'] = poc_ids_grp[index].tolist()
        _clus_info.insert(0, "DATE", date_grp[index], True)
        _clus_info.insert(0, "POC_ID", poc_ids_grp[index], True)
        """

        _data = DATA[DATA.POC_ID.isin(poc_ids_grp[index])]
        _data['Cluster'] = _labels.tolist()
        clustered_data_grp.append(_data)
        index = index + 1
        vol_grp_cnt = vol_grp_cnt + 1
    return clustered_data_grp

def filter_control_pocs(global_ctrl, global_test, min_index,activation_on,limit):
    #print("---------------------------------------------------------------------------")
    #print("Number of Control-POCs before process => "+str(len(global_ctrl)))
    #define the variables
    rmse_val = []
    control = global_ctrl
    ctrl_baseline = global_ctrl.iloc[:,min_index:activation_on]
    test_baseline = global_test.iloc[:,min_index:activation_on]
    #test dataframe
    predicted = test_baseline.iloc[0,:].tolist()

    for i in range(len(ctrl_baseline)):
        actual = ctrl_baseline.iloc[i,:].tolist()
        mse = sklearn.metrics.mean_squared_error(actual, predicted)
        rmse = math.sqrt(mse)
        rmse_val.append(rmse)

    control["RMSE Value"] = rmse_val
    control["RMSE_Rank"] = control["RMSE Value"].rank(method='min')
    control = control[control["RMSE_Rank"]<=limit]
    
    new_ctrl = global_ctrl[global_ctrl["POC_ID"].isin(control["POC_ID"])]
    return new_ctrl

def get_uplift(desc_test, desc_ctrl,min_index, activation_on,activation_end,test_poc, ctrl_pocs,ctrl_outliers,RESTRICT_BASELINE_TO,APT_RESULTS,campaign,retailer,sku):
    activation_on=activation_on-min_index
    if isinstance(test_poc, pd.Series) or isinstance(test_poc, np.ndarray):
        test_poc = int(test_poc.iloc[0] if hasattr(test_poc, 'iloc') else test_poc[0])
    if len(desc_test) == len(desc_ctrl):        
        nRecords = len(desc_test)
        sum_ctrl_y1 = 0
        sum_ctrl_y2 = 0
        sum_test_y1 = 0
        sum_test_y2 = 0
        
        ctrl_zero_y1=0
        ctrl_zero_y2=0
        test_zero_y1=0
        test_zero_y2=0
        baseline_start = 0
        if activation_on>RESTRICT_BASELINE_TO:
            baseline_start=activation_on-RESTRICT_BASELINE_TO
        for nRec in range (baseline_start,activation_on):
            sum_ctrl_y1 = sum_ctrl_y1 + desc_ctrl[nRec]
            sum_test_y1 = sum_test_y1 + desc_test[nRec]
            if (desc_ctrl[nRec]==0):
                ctrl_zero_y1 = ctrl_zero_y1+1
            if (desc_test[nRec]==0):
                test_zero_y1 = test_zero_y1+1
        analysis_end_index=(activation_end-min_index) +1  # -3 because 3 columns exculded,poc_id,DATE,End_Date  
        for nRec in range (activation_on,analysis_end_index):
            sum_ctrl_y2 = sum_ctrl_y2 + desc_ctrl[nRec]
            sum_test_y2 = sum_test_y2 + desc_test[nRec]
            if (desc_ctrl[nRec]==0):
                ctrl_zero_y2 = ctrl_zero_y2+1
            if (desc_test[nRec]==0):
                test_zero_y2 = test_zero_y2+1
        desc_ctrl_1 = desc_ctrl[baseline_start:activation_on]
        desc_ctrl_2 = desc_ctrl[activation_on:analysis_end_index]
        desc_test_1 = desc_test[baseline_start:activation_on]
        desc_test_2 = desc_test[activation_on:analysis_end_index]

        ba_count = activation_on - baseline_start
        aa_count = analysis_end_index - activation_on
        
        avg_ctrl_y1 = sum_ctrl_y1/(ba_count - ctrl_zero_y1)
        avg_test_y1 = sum_test_y1/(ba_count - test_zero_y1)
        
        avg_ctrl_y2 = sum_ctrl_y2/(aa_count - ctrl_zero_y2)
        avg_test_y2 = sum_test_y2/(aa_count - test_zero_y2)
              
        perc_inc_t = 100*(avg_test_y2 - avg_test_y1)/avg_test_y1
        perc_inc_c = 100*(avg_ctrl_y2 - avg_ctrl_y1)/avg_ctrl_y1
        
        test_expected = avg_test_y1*(1 + (perc_inc_c/100))
        lift = 100*(avg_test_y2 - test_expected)/test_expected
        impact = avg_test_y2 - test_expected
               
        _avg_vol = (avg_test_y1+avg_test_y2+avg_ctrl_y1+avg_ctrl_y2)/4
        bs_mths=desc_ctrl_1.shape[0]
        as_mths=desc_ctrl_2.shape[0]
        if np.isinf(lift):
                lift = 0
        desc_test_avg = (desc_test[:-(activation_end-activation_on+1)].mean())
        desc_ctrl_avg = (desc_ctrl[:-(activation_end-activation_on+1)].mean())
        desc_test_dev = []
        desc_ctrl_dev = []
        for j in range(len(desc_test)):
            desc_test_dev.append((desc_test[j] - desc_test_avg)/desc_test_avg)
        for j in range(len(desc_ctrl)):
            desc_ctrl_dev.append((desc_ctrl[j] - desc_ctrl_avg)/desc_ctrl_avg)

        diff = [abs(desc_test_dev[k]-desc_ctrl_dev[k]) for k in range(len(desc_test_dev))]
        score = sum(diff[:-(activation_end-activation_on+1)])
        print("Type of APT_RESULTS:", type(APT_RESULTS))
        print("POC_ID column type:", APT_RESULTS['POC_ID'].dtype)
        print("test_poc type:", type(test_poc))

        try:
            # APT_RESULTS.loc[APT_RESULTS['POC_ID'] == test_poc, 'ABI-TEST AVG VOL'] = (avg_test_y1+avg_test_y2)/2 #desc_test.mean()
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-TEST AVG VOL'] = (avg_test_y1+avg_test_y2)/2
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-AVG VOL'] = _avg_vol
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-%Lift'] = lift
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Estimated impact'] = impact
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Test baseline period'] = avg_test_y1
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Test analysis period'] = avg_test_y2
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Test expected'] = test_expected
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-%Test performance'] = perc_inc_t
        
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control baseline period'] = avg_ctrl_y1
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control analysis period'] = avg_ctrl_y2
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-%Control performance'] = perc_inc_c
        
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Baseline period # weeks with data'] = bs_mths
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Analysis period # weeks with data'] = as_mths
            print("avg_ctrl_y1:", avg_ctrl_y1, "avg_test_y1:", avg_test_y1)
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control count'] = len(ctrl_pocs)
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control Outliers'] = ctrl_outliers
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Goodness of fit score'] = score
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'Campaign'] = campaign
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'Retailer'] = retailer
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'SKU'] = sku
        except Exception as e:
            print("Error after Seven:", e)
        # print(APT_RESULTS.head())
        APT_RESULTS.to_excel(E_path+"\\APT_test.xlsx")
    else:
        print("ERROR : Test and Control are not having same number of columns")
    print(perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol)   
    
    return [perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol]

def get_uplift_val(desc_test, desc_ctrl,min_index,activation_on,validation_start,validation_end,test_poc, ctrl_pocs,RESTRICT_BASELINE_TO,APT_RESULTS):
    if isinstance(test_poc, pd.Series) or isinstance(test_poc, np.ndarray):
        test_poc = int(test_poc.iloc[0] if hasattr(test_poc, 'iloc') else test_poc[0])
    activation_on=activation_on-min_index  
    if len(desc_test) == len(desc_ctrl):        
        nRecords = len(desc_test)
        sum_ctrl_y1 = 0
        sum_ctrl_y2 = 0
        sum_test_y1 = 0
        sum_test_y2 = 0
        
        ctrl_zero_y1=0
        ctrl_zero_y2=0
        test_zero_y1=0
        test_zero_y2=0
        
        baseline_start = 0
        if activation_on>RESTRICT_BASELINE_TO:
            baseline_start=activation_on-RESTRICT_BASELINE_TO
        for nRec in range (baseline_start,activation_on):
            sum_ctrl_y1 = sum_ctrl_y1 + desc_ctrl[nRec]
            sum_test_y1 = sum_test_y1 + desc_test[nRec]
            if (desc_ctrl[nRec]==0):
                ctrl_zero_y1 = ctrl_zero_y1+1
            if (desc_test[nRec]==0):
                test_zero_y1 = test_zero_y1+1
        analysis_end_index=(validation_end-min_index) +1  # -3 because 3 columns exculded,poc_id,DATE,End_Date  
        
        for nRec in range (activation_on,analysis_end_index):
            #print("NREC......................",nRec)
            sum_ctrl_y2 = sum_ctrl_y2 + desc_ctrl[nRec]
            sum_test_y2 = sum_test_y2 + desc_test[nRec]
            if (desc_ctrl[nRec]==0):
                ctrl_zero_y2 = ctrl_zero_y2+1
            if (desc_test[nRec]==0):
                test_zero_y2 = test_zero_y2+1
        
        #print(desc_ctrl)
        desc_ctrl_1 = desc_ctrl[baseline_start:activation_on]
        desc_ctrl_2 = desc_ctrl[activation_on:analysis_end_index]
        desc_test_1 = desc_test[baseline_start:activation_on]
        desc_test_2 = desc_test[activation_on:analysis_end_index]

        ba_count = activation_on - baseline_start
        aa_count = analysis_end_index - activation_on
               
        avg_ctrl_y1 = sum_ctrl_y1/(ba_count - ctrl_zero_y1)
        avg_test_y1 = sum_test_y1/(ba_count - test_zero_y1)
        
        avg_ctrl_y2 = sum_ctrl_y2/(aa_count - ctrl_zero_y2)
        avg_test_y2 = sum_test_y2/(aa_count - test_zero_y2)
              
        perc_inc_t = 100*(avg_test_y2 - avg_test_y1)/avg_test_y1
        perc_inc_c = 100*(avg_ctrl_y2 - avg_ctrl_y1)/avg_ctrl_y1
        
        test_expected = avg_test_y1*(1 + (perc_inc_c/100))
        lift = 100*(avg_test_y2 - test_expected)/test_expected
        impact = avg_test_y2 - test_expected
               
        _avg_vol = (avg_test_y1+avg_test_y2+avg_ctrl_y1+avg_ctrl_y2)/4
        bs_mths=desc_ctrl_1.shape[0]
        as_mths=desc_ctrl_2.shape[0]
        

        if np.isinf(lift):
                lift = 0
        APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-% Validation Period Lift'] = lift
        APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Validation Period Impact'] = impact
        
    else:
        print("ERROR : Test and Control are not having same number of columns")
    
    return [lift]

def get_activation_week_index(date, test_data_columns):
    # Format date to 'YYYY-MM-DD'
    col = date.strftime('%Y-%m-%d')
    
    # Get index of the formatted date
    act_ind = test_data_columns.index(col)
    
    return act_ind

def get_end_week_index(date, test_data_columns):
    # Format date to 'YYYY-MM-DD'
    col = date.strftime('%Y-%m-%d')
    
    # Get index of the formatted date
    end_ind = test_data_columns.index(col)
    
    return end_ind

def lift_outlier_iqr(data):
    upper_bound = 300
    lower_bound = -300
    
    data['Outlier'] = data['ABI-%Lift'].apply(lambda x: "Yes" if (x> upper_bound or x< lower_bound) else "No")
    data['Outlier_Reason'] = data['ABI-%Lift'].apply(lambda x: "Uplift beyond threshold" if (x> upper_bound or x<lower_bound) else "")
    
    data.loc[(data['ABI-Test analysis period'].isna()), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Test analysis period'].isna()), 'Outlier_Reason'] = "Test site has zero-valued in the analysis period"
    
    data.loc[(data['ABI-Control analysis period'].isna()), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Control analysis period'].isna()), 'Outlier_Reason'] = "Control site has zero-valued in the analysis period"

    data.loc[(data['ABI-Test baseline period'].isna()), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Test baseline period'].isna()), 'Outlier_Reason'] = "Test site has zero-valued in the baseline period"
    
    data.loc[(data['ABI-Control baseline period'].isna()), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Control baseline period'].isna()), 'Outlier_Reason'] = "Control site has zero-valued in the baseline period"

    data.loc[(data['ABI-Test analysis period']<0), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Test analysis period']<0), 'Outlier_Reason'] = "Test site has negative data in the analysis period"
    
    data.loc[(data['ABI-Control analysis period']<0), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Control analysis period']<0), 'Outlier_Reason'] = "Control site has negative data in the analysis period"

    data.loc[(data['ABI-Test baseline period']<0), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Test baseline period']<0), 'Outlier_Reason'] = "Test site has negative data in the baseline period"
    
    data.loc[(data['ABI-Control baseline period']<0), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Control baseline period']<0), 'Outlier_Reason'] = "Control site has negative data in the baseline period"
    
    data.loc[(data['ABI-Control count'].isna()), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Control count'].isna()), 'Outlier_Reason'] = "Test site has control POCs below 5"
    
    data.loc[(data['ABI-Control count']<5), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Control count']<5), 'Outlier_Reason'] = "Test site has control POCs below 5"

    return data

def significance_level(data, APT_RESULTS, sku):
    # Filter outliers & nulls
    APT_RESULTS_NO_OUTLIER = data[
        (data["Outlier"] == "No") &
        ~(data["ABI-% Validation Period Lift"].isnull())
    ]
    
    # Prepare the two samples
    data1 = APT_RESULTS_NO_OUTLIER["ABI-% Validation Period Lift"]
    data2 = APT_RESULTS_NO_OUTLIER["ABI-%Lift"]

    print(f"Running Wilcoxon test for SKU: {repr(sku)}")
    print(f"Sample sizes → data1: {len(data1)}, data2: {len(data2)}")
    
    # Compare samples
    stat, p = wilcoxon(data1, data2)
    print(f"Statistics={stat:.3f}, p={p:.3f}")
    
    significance_val = (1 - p) * 100
    print(f"Significance level = {significance_val:.2f}%")
    
    # DEBUG — check matching rows
    # mask = APT_RESULTS['SKU'].astype(str) == str(sku)
    # print(f"Matches found in APT_RESULTS: {mask.sum()}")
    # if mask.sum() > 0:
    #     print("Matching SKU values:", APT_RESULTS.loc[mask, 'SKU'].unique())
    # else:
    #     print("⚠ No matching SKUs found in APT_RESULTS!")
    # Create the column if it doesn't exist
    if 'Significance' not in APT_RESULTS.columns:
        APT_RESULTS['Significance'] = None
    # print("sku passed in:", repr(sku))
    # print("Unique SKUs in APT_RESULTS sample:")
    # print([repr(v) for v in APT_RESULTS['SKU'].unique()[:10]])
    
    # Assign in place
    # APT_RESULTS.loc[mask, 'Significance'] = significance_val
    mask = APT_RESULTS['SKU'].astype(int) == int(sku)
    APT_RESULTS.loc[mask, 'Significance'] = significance_val

    # Check result
    print(APT_RESULTS.loc[mask, ['SKU', 'Significance']])
    
    # Interpret result
    alpha = 0.05
    if p > alpha:
        print('Same distribution (fail to reject H0)')
    else:
        print('Different distribution (reject H0)')

    return significance_val

def accelerate(start_date, end_date, brand, activity_id, campaign, retailer):
    global final_result_output
    print(campaign,activity_id,retailer)
    target_path = os.path.join(E_path,'META')
    if not os.path.isdir(target_path):
        os.makedirs(target_path, exist_ok=True)
    meta = create_pilot_df(activity_id,retailer)
    meta['START DATE'] = start_date
    meta['END DATE'] = end_date
    meta.rename(columns ={'Store_Id':'POC_ID'}, inplace=True)
    meta.to_csv(target_path+f"\\Meta_{activity_id}.csv")
    sku_list = get_sku_list(retailer,brand)
    for sku in sku_list["ITEM CODE"]:
        print("sku :",sku)
        try :
            store_df = create_val_data(sku,retailer)
            Base_data = mod1(store_df)
            Base_data['POC_SAPID'] = pd.to_numeric(Base_data['POC_SAPID'], errors='coerce')
            Base_data = Base_data.dropna(subset=['POC_SAPID'])
            Base_data['POC_SAPID'] = Base_data['POC_SAPID'].astype('int')
            # DATA = DATA.rename(columns={'POC_SAPID':'POC_ID'})
            # print(DATA['Test_Control'].unique())
            DATA, META= read_data(Base_data,meta)
            # print("Base Data")
            # print(Base_data.head())
            # print("Meta")
            # print(META.head())
            print(DATA['Test_Control'].unique())  
            DATA.replace(0,np.nan,inplace=True)
            Test = DATA[DATA['Test_Control']=='Test']
            Control = DATA[DATA['Test_Control']=='Control']
            # print(DATA['Test_Control'].unique())
            META["Start_Date"]=pd.to_datetime(META["Start_Date"])
            META["End_Date"]=pd.to_datetime(META["End_Date"])
            test_start_min_date=META["Start_Date"].min()
            test_end_max_date=META["End_Date"].max()
            min_column,max_column=find_date(test_start_min_date,test_end_max_date)
            RESTRICT_BASELINE_TO=12
            Post_Analysis_DATA=pd.concat([DATA[["POC_ID"]],DATA.iloc[:,DATA.columns.get_loc(max_column)+1:DATA.shape[1]]],axis=1)
            Post_Analysis_DATA = Post_Analysis_DATA.loc[:,~Post_Analysis_DATA.columns.duplicated()]
            if DATA.columns.get_loc(min_column)-RESTRICT_BASELINE_TO<0:
                start=0
            else:
                start=DATA.columns.get_loc(min_column)-RESTRICT_BASELINE_TO
            DATA=pd.concat([DATA[["POC_ID","Start_Date",'End_Date','Test_Control']],DATA.iloc[:,start:DATA.columns.get_loc(max_column)+1]],axis=1)
            DATA = DATA.loc[:,~DATA.columns.duplicated()]
            mid_index=DATA.columns.get_loc(min_column)
            min_index,max_index,min_year,max_year=get_data_indices_n_years(list(Post_Analysis_DATA.columns))
            columns = list(Post_Analysis_DATA)
            Post_Analysis_DATA = replace_nulls_with_0(Post_Analysis_DATA,min_index,max_index,columns)
            min_index,max_index,min_year,max_year=get_data_indices_n_years(list(DATA.columns))
            columns = list(DATA)
            threshold = 70
            DATA=remove_nulls_by_threshold_in_range(DATA, threshold,min_index,mid_index)
            DATA = replace_nulls_with_0(DATA,min_index,max_index,columns)
            # print(DATA.head())
            Test = DATA[DATA['Test_Control']=='Test']
            Control = DATA[DATA['Test_Control']=='Control']
            controlled = DATA[DATA['Test_Control']=='Control']
            test = DATA[DATA['Test_Control']=='Test']
            DATA['NET_AVG_Y1']=DATA.iloc[:,min_index:mid_index].T.mean()
            DATA = DATA[DATA['NET_AVG_Y1']!=0]
            controlled = DATA[DATA['Test_Control']=='Control']
            test = DATA[DATA['Test_Control']=='Test']
            DATA_backup = DATA.copy()
            mid_index=DATA_backup.columns.get_loc(min_column)
            DATA_backup["Baseline Period Avg"] = DATA_backup.iloc[:,min_index:mid_index].T.mean()
            DATA_backup["Promo Period Avg"] = DATA_backup.iloc[:,mid_index:-2].T.mean()
            # print(DATA_backup.head())
            # print(DATA_backup['Test_Control'].unique())
            controlled_backup = DATA_backup[DATA_backup['Test_Control']=='Control']
            test_backup= DATA_backup[DATA_backup['Test_Control']=='Test']
            controlled=controlled_backup
            test=test_backup
            controlled = controlled[controlled['Promo Period Avg']!=0]
            controlled['dev'] = (controlled['Promo Period Avg'] - controlled['Baseline Period Avg'])/controlled['Baseline Period Avg']
            controlled = controlled[controlled['dev']<2]
            controlled.drop(['dev'], axis=1, inplace=True)
            testvscontrol_before=testvscontrolfcn(controlled,test)
# graph
            save_path = os.path.join(E_path,"Plots",f"{activity_id}.png")
            plt.rcParams['figure.figsize']=[18,8]
            plt.plot(testvscontrol_before.T,marker='o')
            plt.xticks(rotation=90)
            plt.legend()
            plt.savefig(save_path, bbox_inches="tight", dpi=300)  
            plt.close()
# graph
            percentile_grp = [0]
            percentile_values_old = []
            percentile_values = []
            data_vol_grp = []
            poc_ids_grp = []
            date_grp = []
            dm_data_grp = []
            dist_mat_grp = []
            num_clusters_grp = []
            kmeans_grp = []
            clustered_data_grp = []
            run_test_pocs = []
            run_ctrl_pocs = []
            data_live = pd.concat([controlled, test])
            data_live = data_live.sort_values(by=['NET_AVG_Y1'])
            test_live = test.sort_values(by=['NET_AVG_Y1'])
            data_live = data_live[data_live.NET_AVG_Y1 != 0]
            test_live = test_live[test_live.NET_AVG_Y1 != 0]
            for p in percentile_grp:
                _q = np.percentile(test_live.NET_AVG_Y1, p)
                percentile_values_old.append(_q)
            a = percentile_values_old
            from operator import add
            t1 = (data_live['NET_AVG_Y1']).sort_values().reset_index().drop(columns='index')
            _l = -1
            _r = -1
            data_vol_grp = []
            for pv in percentile_values:
                _r = pv
                if _l == -1:
                    data_vol_grp.append(data_live[data_live['NET_AVG_Y1'] < _r])
                else:
                    data_vol_grp.append(data_live[(data_live['NET_AVG_Y1'] < _r) & (data_live['NET_AVG_Y1'] >= _l)])
                _l = _r
            data_vol_grp.append(data_live[data_live['NET_AVG_Y1'] >= _r])
            data_vol_grp_copy = data_vol_grp
            dm_data_grp_split = []
            max_poc_count = 2000
            min_poc_count = 200
            for i in range(len(data_vol_grp_copy)):
                if (len(data_vol_grp_copy[i]) <= max_poc_count):
                    dm_data_grp_split.append(data_vol_grp_copy[i])
                else:
                    tot = int(np.ceil(len(data_vol_grp_copy[i])/max_poc_count))
                    min_row_range =0
                    for j in range(tot):
                        max_row_range = min_row_range + max_poc_count
                        if max_row_range > len(data_vol_grp_copy[i]):
                            max_row_range = len(data_vol_grp_copy[i])
                        if (len(data_vol_grp_copy[i]) - max_row_range) < min_poc_count:
                            max_row_range = len(data_vol_grp_copy[i])
                            dm_data_grp_split.append(data_vol_grp_copy[i].iloc[min_row_range:max_row_range,:])
                            break
                        dm_data_grp_split.append(data_vol_grp_copy[i].iloc[min_row_range:max_row_range,:])
                        min_row_range = max_row_range
            dm_data_grp_split = data_vol_grp
            _t_upd = []
            vol_grp_cnt = 1
            for vg in dm_data_grp_split:
                _t = vg.reset_index(drop = True)
                _t_upd.append(_t)
            dm_data_grp_split = _t_upd
            poc_ids_grp = []
            for vg in dm_data_grp_split:
                _poc_id = vg['POC_ID']
                poc_ids_grp.append(_poc_id)
            date_grp = []
            for vg in dm_data_grp_split:
                _poc_id = vg['Start_Date']
                date_grp.append(_poc_id)
            dm_data_grp = []
            for vg in dm_data_grp_split:
                _t=vg
                _t = _t[_t.columns[np.concatenate([range(min_index,max_index)])]]     
                _max_val = _t.iloc[:,:].T.max()
                _t.iloc[:,:] = _t.iloc[:,:].div(_max_val, axis=0)
                dm_data_grp.append(_t)
            percentile_values=[]
            kmeans_grp=[]
            dist_mat_grp = get_dist_mat_grp(dm_data_grp)
            # print(dist_mat_grp)
            # with open(path+"\\test_v4.txt", "wb") as fp:   #Pickling
                # pickle.dump(dist_mat_grp, fp)
            num_clusters_grp = []    
            num_clusters_grp = get_optimal_n_cluster(dist_mat_grp)
            clustered_data_grp = [] 
            # print(DATA.head())
            print(DATA['Test_Control'].unique())
            # DATA.to_excel(E_path,"\\Data_check.xlsx")   
            clustered_data_grp = get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp,kmeans_grp,DATA)
            total_test_pocs = DATA[DATA['Test_Control']=='Test'].POC_ID.tolist()
            print(len(total_test_pocs))
            data_len = len(clustered_data_grp)
            test_pocs_len = len(total_test_pocs)
            APT_RESULTS=META.copy()
            APT_RESULTS=APT_RESULTS[APT_RESULTS.POC_ID.isin(total_test_pocs)]
            APT_RESULTS["Start_Date"]=pd.to_datetime(APT_RESULTS["Start_Date"])
            APT_RESULTS["End_Date"]=pd.to_datetime(APT_RESULTS["End_Date"])
            APT_RESULTS.drop(['Start_Date', 'End_Date'], axis=1, inplace=True)
            # print(META.head())
            # print("APT_RESULTS")
            # print(APT_RESULTS.head())
            clustered_data_grp[0].Cluster = 0
            clustered_data_grp[0]
            clustered_data_grp[0].to_excel(E_path+"\\Clustered_Data"+f"\\clustered_group_0_{activity_id}_{sku}.xlsx", index=False)
            i=0
            limit = 5
            test_control_list = pd.DataFrame()
            for test_poc in total_test_pocs: 
                i=i+1
                if(i%100==0):
                    print("Test POC No:",i)
                for di in range(0,data_len):
                    g = clustered_data_grp[di]
                    # print(g['Test_Control'].unique())
                    if (len(g[g.POC_ID==test_poc])>0):
                        global_test = g[g.POC_ID == str(test_poc)]
                        global_ctrl = g[g.Test_Control=='Control']
                        # print(len(global_ctrl))           
                        if len(global_ctrl) > 0:
                            ctrl_len_before_filter = len(global_ctrl)
                            global_test["Start_Date"]=pd.to_datetime(global_test["Start_Date"])
                            global_test["End_Date"]=pd.to_datetime(global_test["End_Date"])
                            activation_on = get_activation_week_index(global_test.Start_Date.iloc[0], list(global_test))
                            activation_end=get_end_week_index(global_test.End_Date.iloc[0], list(global_test))                
                            if len(global_ctrl) > limit:
                                global_ctrl = filter_control_pocs(global_ctrl, global_test,min_index,activation_on,limit)
                            ctrl_len_after_filter = len(global_ctrl)
                            ctrl_outliers = ctrl_len_before_filter - ctrl_len_after_filter
                            if((activation_end - activation_on) + 1> 12):
                                validation_period = 2
                            else:
                                validation_period = 1
                            validation_start = activation_on - validation_period
                            validation_end = activation_on
                            global_ctrl_test = global_ctrl
                            global_ctrl_test['Test_POC_ID'] = test_poc
                            control_list = global_ctrl_test[["Test_POC_ID","POC_ID"]]
                            test_control_list = pd.concat([test_control_list, control_list])
                            desc_test = global_test[global_test.columns[np.concatenate([range(min_index,max_index+1)])]].mean()
                            desc_ctrl = global_ctrl[global_ctrl.columns[np.concatenate([range(min_index,max_index+1)])]].mean()
                            # print("ABC")
                            perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol = get_uplift(desc_test,desc_ctrl,min_index,activation_on,activation_end,global_test.POC_ID, global_ctrl.POC_ID,ctrl_outliers,RESTRICT_BASELINE_TO,APT_RESULTS,campaign,retailer,sku)
                            # print("GHI")
                            validation_lift= get_uplift_val(desc_test, desc_ctrl,min_index,activation_on,validation_start,validation_end,global_test.POC_ID, global_ctrl.POC_ID,RESTRICT_BASELINE_TO,APT_RESULTS)
            col= ['Campaign_ID','Test_Control','Start_Date','End_Date']
            print("XYZ")
            print(META.columns.tolist())
            temp=META.drop(col, axis=1)
            print("dropped")
            # ['Campaign_ID', 'POC_ID', 'Test_Control', 'Start_Date', 'End_Date']
            try:
                test_control_list = test_control_list.rename(columns={'POC_ID': 'Control_POC_ID'})
                test_control_list_temp = test_control_list.merge(temp, left_on='Test_POC_ID', right_on='POC_ID', how='left')
                test_control_list_temp = test_control_list_temp.rename(columns={'GEOGRAPHY_DESCRIPTION':'Test_GEOGRAPHY_DESCRIPTION','STORE_NO': 'Test_Store_No'})
                test_control_list_final = test_control_list_temp.merge(temp, left_on='Control_POC_ID', right_on='POC_ID', how='left')
                test_control_list_final = test_control_list_final.rename(columns={'GEOGRAPHY_DESCRIPTION_y':'Control_GEOGRAPHY_DESCRIPTION','STORE_NO': 'Control_Store_No'})
                test_control_list_final= test_control_list_final.drop(['POC_ID_x','POC_ID_y'],axis=1)
            except:
                continue
            print("next_sig")
            APT_Outlier = lift_outlier_iqr(APT_RESULTS)
            sig_level = significance_level(APT_Outlier,APT_RESULTS,sku)
            print("Sig Level calculated")
            # final_result_output = pd.DataFrame(columns=APT_RESULTS.columns)
            final_result_output = pd.concat([final_result_output, APT_RESULTS], ignore_index=True)
            test_control_list.to_excel(E_path+"\\TestvCtrl"+f"\\{activity_id}.xlsx", sheet_name="Control_Mapping", index=False)
        except:
            continue
    final_result_output.to_excel(E_path+"\\Results"+f"\\Final_Results_{activity_id}.xlsx",sheet_name="Results", index=False)

for _, row in activity_df.iterrows():
    accelerate(row['Nielsen Start'], row['Nielsen End'], row['Brand'], row['Activity UID'],row['Campaign'],row['Retailer'])
    
final_result_output.to_excel(E_path+"\\Results"+"\\Final_Results.xlsx",sheet_name="Results", index=False)

print("The End")
from datetime import datetime
current_datetime = datetime.now()
formatted_timestamp = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
print(f"Formatted Timestamp: {formatted_timestamp}")