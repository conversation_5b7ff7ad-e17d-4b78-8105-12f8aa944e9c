"""
Optimized utility functions for the Accelerate analysis pipeline.
Contains date handling, data validation, and helper functions with performance improvements.

Performance Improvements:
- Vectorized date operations
- Optimized null value processing
- Cached computations
- Memory-efficient data transformations
"""

import pandas as pd
import numpy as np
from datetime import datetime
from functools import lru_cache

from .config_optimized import THRESHOLD_PERCENTAGE, CACHE_SIZE
from .performance_monitor import timed_operation

@lru_cache(maxsize=CACHE_SIZE)
def find_date(min_date, max_date):
    """
    Optimized version of find_date with caching.
    """
    if isinstance(min_date, str):
        min_date = datetime.strptime(min_date, '%Y-%m-%d')
    if isinstance(max_date, str):
        max_date = datetime.strptime(max_date, '%Y-%m-%d')
    
    return min_date.strftime('%Y-%m-%d'), max_date.strftime('%Y-%m-%d')

@timed_operation("activation_week_index")
def get_activation_week_index(meta_data, min_index):
    """
    Optimized version to get activation week index.
    """
    if 'Start_Date' not in meta_data.columns:
        return min_index + 12  # Default fallback
    
    # Vectorized date operations
    start_dates = pd.to_datetime(meta_data['Start_Date'], errors='coerce')
    
    if start_dates.isna().all():
        return min_index + 12
    
    # Get the most common start date
    most_common_date = start_dates.mode().iloc[0] if len(start_dates.mode()) > 0 else start_dates.iloc[0]
    
    # Calculate week index (simplified calculation)
    base_date = datetime(2020, 1, 1)  # Reference date
    weeks_diff = (most_common_date - base_date).days // 7
    
    return max(min_index, weeks_diff)

@timed_operation("null_removal")
def remove_nulls_by_threshold_in_range(data, start_index, end_index, threshold=THRESHOLD_PERCENTAGE):
    """
    Optimized version of null removal with vectorized operations.
    """
    if start_index >= end_index or start_index < 0:
        return data
    
    # Get numeric columns in the specified range
    numeric_cols = data.select_dtypes(include=[np.number]).columns
    
    if len(numeric_cols) == 0:
        return data
    
    # Calculate range bounds safely
    max_col_index = len(numeric_cols) - 1
    actual_start = max(0, min(start_index, max_col_index))
    actual_end = min(end_index, max_col_index + 1)
    
    if actual_start >= actual_end:
        return data
    
    # Get columns in range
    range_cols = numeric_cols[actual_start:actual_end]
    
    if len(range_cols) == 0:
        return data
    
    # Vectorized null percentage calculation
    null_percentages = data[range_cols].isnull().sum(axis=1) / len(range_cols) * 100
    
    # Filter rows below threshold
    valid_rows = null_percentages <= threshold
    
    filtered_data = data[valid_rows].copy()
    
    print(f"Removed {len(data) - len(filtered_data)} rows with >{threshold}% nulls in range")
    
    return filtered_data

@timed_operation("data_validation")
def validate_data_quality(data, min_rows=10, min_cols=5):
    """
    Validate data quality with optimized checks.
    """
    issues = []
    
    # Basic shape validation
    if len(data) < min_rows:
        issues.append(f"Insufficient rows: {len(data)} < {min_rows}")
    
    if len(data.columns) < min_cols:
        issues.append(f"Insufficient columns: {len(data.columns)} < {min_cols}")
    
    # Vectorized null checks
    null_percentages = data.isnull().sum() / len(data) * 100
    high_null_cols = null_percentages[null_percentages > 50].index.tolist()
    
    if high_null_cols:
        issues.append(f"High null columns: {high_null_cols}")
    
    # Check for duplicate rows
    duplicate_count = data.duplicated().sum()
    if duplicate_count > len(data) * 0.1:  # More than 10% duplicates
        issues.append(f"High duplicate rows: {duplicate_count}")
    
    # Check data types
    object_cols = data.select_dtypes(include=['object']).columns
    if len(object_cols) > len(data.columns) * 0.8:  # More than 80% object columns
        issues.append("Too many non-numeric columns")
    
    return {
        'is_valid': len(issues) == 0,
        'issues': issues,
        'row_count': len(data),
        'col_count': len(data.columns),
        'null_percentage': data.isnull().sum().sum() / (len(data) * len(data.columns)) * 100
    }

@timed_operation("outlier_detection")
def detect_outliers_iqr(data, columns=None, multiplier=1.5):
    """
    Optimized outlier detection using IQR method with vectorized operations.
    """
    if columns is None:
        columns = data.select_dtypes(include=[np.number]).columns
    
    outlier_mask = pd.Series(False, index=data.index)
    outlier_info = {}
    
    for col in columns:
        if col not in data.columns:
            continue
        
        # Vectorized IQR calculation
        Q1 = data[col].quantile(0.25)
        Q3 = data[col].quantile(0.75)
        IQR = Q3 - Q1
        
        # Calculate bounds
        lower_bound = Q1 - multiplier * IQR
        upper_bound = Q3 + multiplier * IQR
        
        # Identify outliers
        col_outliers = (data[col] < lower_bound) | (data[col] > upper_bound)
        outlier_mask |= col_outliers
        
        outlier_info[col] = {
            'count': col_outliers.sum(),
            'percentage': col_outliers.sum() / len(data) * 100,
            'lower_bound': lower_bound,
            'upper_bound': upper_bound
        }
    
    return {
        'outlier_mask': outlier_mask,
        'outlier_info': outlier_info,
        'total_outliers': outlier_mask.sum()
    }

@timed_operation("data_preprocessing")
def preprocess_data_optimized(data, remove_outliers=True, fill_nulls=True, optimize_dtypes=True):
    """
    Comprehensive data preprocessing with optimizations.
    """
    processed_data = data.copy()
    preprocessing_log = []
    
    # Remove outliers if requested
    if remove_outliers:
        outlier_result = detect_outliers_iqr(processed_data)
        if outlier_result['total_outliers'] > 0:
            processed_data = processed_data[~outlier_result['outlier_mask']]
            preprocessing_log.append(f"Removed {outlier_result['total_outliers']} outliers")
    
    # Fill nulls if requested
    if fill_nulls:
        numeric_cols = processed_data.select_dtypes(include=[np.number]).columns
        
        # Fill numeric columns with median (vectorized)
        for col in numeric_cols:
            null_count = processed_data[col].isnull().sum()
            if null_count > 0:
                processed_data[col].fillna(processed_data[col].median(), inplace=True)
                preprocessing_log.append(f"Filled {null_count} nulls in {col}")
        
        # Fill categorical columns with mode
        categorical_cols = processed_data.select_dtypes(include=['object', 'category']).columns
        for col in categorical_cols:
            null_count = processed_data[col].isnull().sum()
            if null_count > 0:
                mode_value = processed_data[col].mode().iloc[0] if len(processed_data[col].mode()) > 0 else 'Unknown'
                processed_data[col].fillna(mode_value, inplace=True)
                preprocessing_log.append(f"Filled {null_count} nulls in {col}")
    
    # Optimize data types if requested
    if optimize_dtypes:
        from .data_processing_optimized import optimize_dataframe_dtypes
        original_memory = processed_data.memory_usage(deep=True).sum()
        processed_data = optimize_dataframe_dtypes(processed_data)
        new_memory = processed_data.memory_usage(deep=True).sum()
        memory_saved = original_memory - new_memory
        preprocessing_log.append(f"Optimized dtypes, saved {memory_saved / 1024**2:.2f}MB")
    
    return processed_data, preprocessing_log

def calculate_correlation_matrix(data, method='pearson', min_periods=10):
    """
    Optimized correlation matrix calculation.
    """
    numeric_data = data.select_dtypes(include=[np.number])
    
    if len(numeric_data.columns) < 2:
        return pd.DataFrame()
    
    # Vectorized correlation calculation
    correlation_matrix = numeric_data.corr(method=method, min_periods=min_periods)
    
    return correlation_matrix

def find_highly_correlated_features(correlation_matrix, threshold=0.95):
    """
    Find highly correlated feature pairs efficiently.
    """
    # Get upper triangle of correlation matrix
    upper_triangle = correlation_matrix.where(
        np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool)
    )
    
    # Find highly correlated pairs
    high_corr_pairs = []
    for col in upper_triangle.columns:
        high_corr_rows = upper_triangle.index[abs(upper_triangle[col]) > threshold].tolist()
        for row in high_corr_rows:
            high_corr_pairs.append({
                'feature1': row,
                'feature2': col,
                'correlation': upper_triangle.loc[row, col]
            })
    
    return high_corr_pairs

@lru_cache(maxsize=CACHE_SIZE)
def get_date_features(date_str):
    """
    Extract date features with caching.
    """
    try:
        date_obj = pd.to_datetime(date_str)
        return {
            'year': date_obj.year,
            'month': date_obj.month,
            'quarter': date_obj.quarter,
            'day_of_year': date_obj.dayofyear,
            'week_of_year': date_obj.isocalendar()[1],
            'is_weekend': date_obj.weekday() >= 5
        }
    except:
        return None

def create_time_series_features(data, date_column, value_columns):
    """
    Create time series features efficiently.
    """
    if date_column not in data.columns:
        return data
    
    enhanced_data = data.copy()
    
    # Convert date column
    enhanced_data[date_column] = pd.to_datetime(enhanced_data[date_column], errors='coerce')
    
    # Sort by date
    enhanced_data = enhanced_data.sort_values(date_column)
    
    # Create lag features for each value column
    for col in value_columns:
        if col in enhanced_data.columns:
            # Vectorized lag features
            enhanced_data[f'{col}_lag1'] = enhanced_data[col].shift(1)
            enhanced_data[f'{col}_lag7'] = enhanced_data[col].shift(7)  # Weekly lag
            enhanced_data[f'{col}_ma7'] = enhanced_data[col].rolling(window=7, min_periods=1).mean()
            enhanced_data[f'{col}_ma30'] = enhanced_data[col].rolling(window=30, min_periods=1).mean()
            
            # Growth rates
            enhanced_data[f'{col}_growth'] = enhanced_data[col].pct_change()
    
    return enhanced_data
