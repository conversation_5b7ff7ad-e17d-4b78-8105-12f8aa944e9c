{"execution_summary": {"total_execution_time": 117.24304938316345, "peak_memory_usage_gb": 16.55832290649414, "total_operations": 843, "operations_per_second": 7.190191695244819, "garbage_collections": 50}, "timing_breakdown": {"create_pilot_df_optimized": {"total_time": 0.19720029830932617, "average_time": 0.004809763373398199, "min_time": 0.0006573200225830078, "max_time": 0.01478886604309082, "count": 41, "std_dev": 0.002178400285350942}, "create_val_data_optimized": {"total_time": 8.944125890731812, "average_time": 0.011893784429164642, "min_time": 0.0, "max_time": 0.205125093460083, "count": 752, "std_dev": 0.02156812368145522}, "mod1_optimized": {"total_time": 704.3238790035248, "average_time": 1.8389657415235634, "min_time": 0.3712778091430664, "max_time": 3.2725045680999756, "count": 383, "std_dev": 0.6585214093198627}, "read_data_optimized": {"total_time": 41.92930030822754, "average_time": 0.10947597991704318, "min_time": 0.010072708129882812, "max_time": 0.4536750316619873, "count": 383, "std_dev": 0.08622567434979389}}, "memory_analysis": {"peak_memory_gb": 16.55832290649414, "average_memory_gb": 16.46091590549635, "min_memory_gb": 16.09136199951172, "memory_variance": 0.006190068556129182, "memory_trend": "increasing"}, "resource_utilization": {"average_cpu_percent": 6.439130434782609, "peak_cpu_percent": 10.2, "cpu_utilization_efficiency": "low"}, "bottlenecks": [], "recommendations": ["High memory usage detected - consider processing data in smaller chunks", "Low CPU utilization - consider increasing parallel processing"]}