import pandas as pd
import numpy as np
from scipy.stats import wil<PERSON>xon

def lift_outlier_iqr(data):
    upper_bound = 300
    lower_bound = -300
    
    data['Outlier'] = data['ABI-%Lift'].apply(lambda x: "Yes" if (x > upper_bound or x < lower_bound) else "No")
    data['Outlier_Reason'] = data['ABI-%Lift'].apply(lambda x: "Uplift beyond threshold" if (x > upper_bound or x < lower_bound) else "")
    
    data.loc[(data['ABI-Test analysis period'].isna()), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Test analysis period'].isna()), 'Outlier_Reason'] = "Test site has zero-valued in the analysis period"
    
    data.loc[(data['ABI-Control analysis period'].isna()), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Control analysis period'].isna()), 'Outlier_Reason'] = "Control site has zero-valued in the analysis period"

    data.loc[(data['ABI-Test baseline period'].isna()), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Test baseline period'].isna()), 'Outlier_Reason'] = "Test site has zero-valued in the baseline period"
    
    data.loc[(data['ABI-Control baseline period'].isna()), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Control baseline period'].isna()), 'Outlier_Reason'] = "Control site has zero-valued in the baseline period"

    data.loc[(data['ABI-Test analysis period'] < 0), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Test analysis period'] < 0), 'Outlier_Reason'] = "Test site has negative data in the analysis period"
    
    data.loc[(data['ABI-Control analysis period'] < 0), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Control analysis period'] < 0), 'Outlier_Reason'] = "Control site has negative data in the analysis period"

    data.loc[(data['ABI-Test baseline period'] < 0), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Test baseline period'] < 0), 'Outlier_Reason'] = "Test site has negative data in the baseline period"
    
    data.loc[(data['ABI-Control baseline period'] < 0), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Control baseline period'] < 0), 'Outlier_Reason'] = "Control site has negative data in the baseline period"
    
    data.loc[(data['ABI-Control count'].isna()), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Control count'].isna()), 'Outlier_Reason'] = "Test site has control POCs below 5"
    
    data.loc[(data['ABI-Control count'] < 5), 'Outlier'] = "Yes"  
    data.loc[(data['ABI-Control count'] < 5), 'Outlier_Reason'] = "Test site has control POCs below 5"

    return data

def significance_level(data, APT_RESULTS, sku):
    APT_RESULTS_NO_OUTLIER = data[
        (data["Outlier"] == "No") &
        ~(data["ABI-% Validation Period Lift"].isnull())
    ]
    
    data1 = APT_RESULTS_NO_OUTLIER["ABI-% Validation Period Lift"]
    data2 = APT_RESULTS_NO_OUTLIER["ABI-%Lift"]

    stat, p = wilcoxon(data1, data2)
    significance_val = (1 - p) * 100
    
    if 'Significance' not in APT_RESULTS.columns:
        APT_RESULTS['Significance'] = None
        
    mask = APT_RESULTS['SKU'].astype(int) == int(sku)
    APT_RESULTS.loc[mask, 'Significance'] = significance_val

    return significance_val