# Refactoring Plan for Accelerate_Auto1.py

## Executive Summary

This document outlines a comprehensive refactoring plan to transform the monolithic `Accelerate_Auto1.py` script (886 lines) into a modular, maintainable architecture. The refactoring will improve code organization, reusability, testability, and maintainability while preserving all existing functionality.

## Current State Analysis

### Issues with Current Implementation
- **Monolithic Structure**: Single 886-line file with mixed responsibilities
- **Global Variables**: Hardcoded paths and configuration scattered throughout
- **Poor Separation of Concerns**: Data loading, processing, and analysis mixed together
- **Limited Reusability**: Functions tightly coupled to specific file structures
- **Difficult Testing**: No clear separation for unit testing individual components
- **Maintenance Challenges**: Changes require understanding the entire codebase

### Current Key Components
The existing script contains 25+ functions handling:
- Data loading and preparation
- Data cleaning and filtering
- Control group matching using DTW and K-Means
- Uplift calculation and validation
- Statistical analysis and outlier detection
- File I/O and result generation

## Proposed Modular Architecture

```mermaid
graph TD
    A[main.py] --> B[config.py]
    A --> C[data_loader.py]
    A --> D[data_processor.py]
    A --> E[control_matcher.py]
    A --> F[uplift_calculator.py]
    A --> G[statistical_analyzer.py]
    A --> H[result_exporter.py]
    A --> I[utils.py]
    
    C --> B
    D --> B
    D --> I
    E --> I
    F --> I
    G --> I
    H --> B
```

## New File Structure

### 1. `config.py` - Configuration Management
**Purpose**: Centralize all configuration settings, file paths, and constants.

**Functions to Move**:
- **New Configuration Class**: `Config`
  - Centralize `E_path` and all hardcoded paths
  - Define thresholds (70% null threshold, RMSE limit=5, etc.)
  - File naming patterns and directory structures
  - Analysis parameters (RESTRICT_BASELINE_TO=12, validation_period logic)

**New Constants**:
```python
class Config:
    BASE_PATH = r"C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\UK Accelerate Automated"
    ACTIVITY_FILE = "Activity_ID_List_1.xlsx"
    TEST_STORE_FILE = "Test Store List.xlsx"
    SKU_LIST_FILE = "SKU List.csv"
    NULL_THRESHOLD = 70
    RESTRICT_BASELINE_TO = 12
    CONTROL_LIMIT = 5
    OUTLIER_UPPER_BOUND = 300
    OUTLIER_LOWER_BOUND = -300
```

### 2. `data_loader.py` - Data Loading and Input Management
**Purpose**: Handle all data loading operations from various input sources.

**Functions to Move**:
- `create_pilot_df(activity_id, retailer)` → Move from line 51
- `get_sku_list(retailer, brand)` → Move from line 65
- `create_val_data(sku, retailer)` → Move from line 69

**New Functions to Create**:
- `load_activity_data()` → Extract activity_df loading logic
- `validate_input_files()` → Ensure all required files exist
- `load_store_codes(retailer)` → Extract store code loading logic

### 3. `data_processor.py` - Data Transformation and Preparation
**Purpose**: Handle data cleaning, transformation, and preparation for analysis.

**Functions to Move**:
- `mod1(store_df)` → Move from line 87
- `read_data(Base_data, meta)` → Move from line 112
- `find_date(min_date, max_date)` → Move from line 144
- `remove_nulls_by_threshold_in_range(data, threshold, min_index, data_end_index)` → Move from line 153
- `replace_nulls_with_0(data, min_index, end_index, columns)` → Move from line 159
- `get_data_indices_n_years(columns)` → Move from line 164

**New Functions to Create**:
- `prepare_test_control_data(data)` → Extract test/control separation logic
- `filter_baseline_data(data, min_date, restrict_baseline_to)` → Extract baseline filtering
- `calculate_volume_averages(data)` → Extract NET_AVG_Y1 calculation logic

### 4. `control_matcher.py` - Control Group Selection and Matching
**Purpose**: Implement DTW-based control group matching using clustering algorithms.

**Functions to Move**:
- `get_dist_mat_grp(dm_data_grp)` → Move from line 210
- `get_optimal_n_cluster(dist_mat_grp)` → Move from line 239
- `get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, DATA)` → Move from line 275
- `filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)` → Move from line 303

**New Functions to Create**:
- `prepare_clustering_groups(data_live, percentile_grp)` → Extract volume grouping logic
- `split_large_groups(data_vol_grp, max_poc_count, min_poc_count)` → Extract group splitting logic
- `normalize_volume_data(dm_data_grp, min_index, max_index)` → Extract normalization logic

### 5. `uplift_calculator.py` - Uplift and Impact Analysis
**Purpose**: Calculate sales uplift, impact, and validation metrics.

**Functions to Move**:
- `get_uplift(desc_test, desc_ctrl, min_index, activation_on, activation_end, test_poc, ctrl_pocs, ctrl_outliers, RESTRICT_BASELINE_TO, APT_RESULTS, campaign, retailer, sku)` → Move from line 327
- `get_uplift_val(desc_test, desc_ctrl, min_index, activation_on, validation_start, validation_end, test_poc, ctrl_pocs, RESTRICT_BASELINE_TO, APT_RESULTS)` → Move from line 435
- `testvscontrolfcn(control_data, test_data)` → Move from line 183
- `get_activation_week_index(date, test_data_columns)` → Move from line 509
- `get_end_week_index(date, test_data_columns)` → Move from line 518

**New Functions to Create**:
- `calculate_baseline_metrics(desc_test, desc_ctrl, baseline_start, activation_on)` → Extract baseline calculation
- `calculate_analysis_metrics(desc_test, desc_ctrl, activation_on, analysis_end)` → Extract analysis period calculation
- `calculate_percentage_changes(avg_test_y1, avg_test_y2, avg_ctrl_y1, avg_ctrl_y2)` → Extract percentage calculation logic

### 6. `statistical_analyzer.py` - Statistical Analysis and Validation
**Purpose**: Perform statistical tests, outlier detection, and significance analysis.

**Functions to Move**:
- `lift_outlier_iqr(data)` → Move from line 527
- `significance_level(data, APT_RESULTS, sku)` → Move from line 566

**New Functions to Create**:
- `detect_outliers_by_business_rules(data)` → Extract business rule outlier detection
- `perform_wilcoxon_test(validation_lift, primary_lift)` → Extract Wilcoxon test logic
- `calculate_goodness_of_fit(desc_test, desc_ctrl)` → Extract goodness of fit calculation
- `validate_data_quality(data)` → Add data quality validation

### 7. `result_exporter.py` - Results Output and Visualization
**Purpose**: Handle all output file generation and visualization.

**New Functions to Create**:
- `generate_test_control_plot(testvscontrol_data, activity_id, save_path)` → Extract plotting logic from line 698-704
- `export_results_to_excel(final_results, activity_id, base_path)` → Extract Excel export logic
- `export_control_mapping(test_control_list, activity_id, base_path)` → Extract control mapping export
- `export_clustered_data(clustered_data, activity_id, sku, base_path)` → Extract clustered data export
- `create_output_directories(base_path)` → Ensure output directories exist

### 8. `utils.py` - Utility Functions and Helpers
**Purpose**: Provide common utility functions used across modules.

**New Functions to Create**:
- `setup_pandas_options()` → Extract pandas configuration from lines 35-37
- `print_timestamp(message)` → Extract timestamp printing logic
- `validate_date_format(date_string)` → Add date validation
- `safe_numeric_conversion(value, default=0)` → Add safe type conversion
- `log_processing_step(step_name, duration)` → Add logging functionality

### 9. `main.py` - Main Orchestration Script
**Purpose**: Orchestrate the entire workflow by importing and calling functions from modules.

**Core Structure**:
```python
def main():
    """Main orchestration function"""
    # Load configuration
    config = Config()
    
    # Load activity data
    activity_df = data_loader.load_activity_data(config.ACTIVITY_FILE)
    
    # Process each campaign
    for _, row in activity_df.iterrows():
        process_campaign(
            start_date=row['Nielsen Start'],
            end_date=row['Nielsen End'],
            brand=row['Brand'],
            activity_id=row['Activity UID'],
            campaign=row['Campaign'],
            retailer=row['Retailer'],
            config=config
        )

def process_campaign(start_date, end_date, brand, activity_id, campaign, retailer, config):
    """Process a single campaign - replaces the accelerate() function"""
    # This function will contain the refactored logic from accelerate()
    # broken down into clear steps using the new modules
```

## Migration Strategy

### Phase 1: Configuration and Utilities (Week 1)
1. Create `config.py` with all constants and paths
2. Create `utils.py` with common helper functions
3. Test configuration loading and utility functions

### Phase 2: Data Management (Week 2)
1. Create `data_loader.py` and migrate loading functions
2. Create `data_processor.py` and migrate transformation functions
3. Update imports and test data loading pipeline

### Phase 3: Core Analysis (Week 3)
1. Create `control_matcher.py` and migrate clustering functions
2. Create `uplift_calculator.py` and migrate calculation functions
3. Test control matching and uplift calculation independently

### Phase 4: Analysis and Output (Week 4)
1. Create `statistical_analyzer.py` and migrate statistical functions
2. Create `result_exporter.py` and migrate output functions
3. Create `main.py` with orchestration logic

### Phase 5: Testing and Validation (Week 5)
1. Create unit tests for each module
2. Integration testing with sample data
3. Performance comparison with original script
4. Documentation and final cleanup

## Benefits of the Refactored Architecture

### 1. **Improved Maintainability**
- Clear separation of concerns
- Easier to locate and modify specific functionality
- Reduced cognitive load when making changes

### 2. **Enhanced Testability**
- Individual modules can be unit tested
- Mock data can be used for testing specific components
- Easier debugging and error isolation

### 3. **Better Reusability**
- Functions can be reused across different analysis scenarios
- Configuration can be easily modified for different environments
- Components can be used independently

### 4. **Scalability**
- New analysis methods can be added without affecting existing code
- Parallel processing can be implemented more easily
- Memory usage can be optimized per module

### 5. **Code Quality**
- Consistent coding standards across modules
- Better error handling and logging
- Improved documentation and type hints

## Implementation Guidelines

### Code Standards
- Use type hints for all function parameters and return values
- Implement comprehensive error handling with custom exceptions
- Add docstrings following Google style guide
- Use logging instead of print statements for better debugging

### Testing Strategy
- Unit tests for each function with at least 80% coverage
- Integration tests for module interactions
- Performance tests to ensure no regression
- Mock data for testing edge cases

### Documentation Requirements
- API documentation for each module
- Usage examples for complex functions
- Configuration guide for different environments
- Migration guide for users of the original script

## Risk Mitigation

### Potential Risks
1. **Data Processing Differences**: Refactoring might introduce subtle changes in calculation logic
2. **Performance Impact**: Additional function calls might affect processing speed
3. **Configuration Errors**: Centralized configuration might introduce new failure points

### Mitigation Strategies
1. **Comprehensive Testing**: Compare outputs between original and refactored versions
2. **Performance Monitoring**: Benchmark critical functions and optimize if needed
3. **Configuration Validation**: Implement validation for all configuration parameters
4. **Gradual Migration**: Implement and test modules incrementally

## Success Metrics

### Quantitative Metrics
- **Code Maintainability**: Reduce cyclomatic complexity from current high levels to <10 per function
- **Test Coverage**: Achieve >85% code coverage across all modules
- **Performance**: Maintain <5% performance degradation compared to original script
- **Code Duplication**: Reduce code duplication to <3%

### Qualitative Metrics
- **Developer Experience**: Easier onboarding for new team members
- **Debugging Efficiency**: Faster issue resolution and error identification
- **Feature Development**: Reduced time to implement new analysis features
- **System Reliability**: More predictable behavior and better error handling

## Conclusion

This refactoring plan transforms a monolithic 886-line script into a modular, maintainable architecture consisting of 9 focused modules. The new structure improves code organization, testability, and maintainability while preserving all existing functionality. The phased implementation approach minimizes risk and ensures a smooth transition from the current system.

The refactored codebase will be more scalable, easier to understand, and significantly more maintainable, setting a strong foundation for future enhancements to the marketing campaign analysis system.