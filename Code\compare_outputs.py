"""
Comparison script to verify that the modular version produces identical output
to the original monolithic version.

This script can be used to test specific functions or run small-scale comparisons.
"""

import pandas as pd
import numpy as np
from datetime import datetime

def test_individual_functions():
    """Test individual functions to ensure they work identically."""
    print("Testing individual functions...")
    
    # Test config
    from accelerate_modules.config import E_path
    print(f"✅ E_path: {E_path}")
    
    # Test utils functions
    from accelerate_modules.utils import find_date
    min_date = datetime(2023, 1, 1)
    max_date = datetime(2023, 12, 31)
    min_col, max_col = find_date(min_date, max_date)
    print(f"✅ Date formatting: {min_col} to {max_col}")
    
    # Test data processing functions (without actual data files)
    from accelerate_modules.data_processing import get_sku_list
    print("✅ Data processing functions imported successfully")
    
    # Test analysis functions
    from accelerate_modules.analysis import testvscontrolfcn
    print("✅ Analysis functions imported successfully")
    
    return True

def compare_function_signatures():
    """Compare function signatures between original and modular versions."""
    print("\nComparing function signatures...")
    
    # Import modular functions
    from accelerate_modules.data_processing import create_pilot_df, get_sku_list, create_val_data, mod1, read_data
    from accelerate_modules.analysis import get_uplift, significance_level, lift_outlier_iqr
    from accelerate_modules.utils import find_date, get_activation_week_index
    
    # Check that functions exist and are callable
    functions_to_check = [
        create_pilot_df, get_sku_list, create_val_data, mod1, read_data,
        get_uplift, significance_level, lift_outlier_iqr,
        find_date, get_activation_week_index
    ]
    
    for func in functions_to_check:
        if callable(func):
            print(f"✅ {func.__name__} is callable")
        else:
            print(f"❌ {func.__name__} is not callable")
            return False
    
    return True

def test_data_structures():
    """Test that data structures and constants match."""
    print("\nTesting data structures and constants...")
    
    from accelerate_modules.config import (
        RESTRICT_BASELINE_TO, THRESHOLD_PERCENTAGE, MAX_POC_COUNT, 
        MIN_POC_COUNT, CONTROL_LIMIT, UPPER_LIFT_BOUND, LOWER_LIFT_BOUND
    )
    
    # Verify constants match original values
    expected_values = {
        'RESTRICT_BASELINE_TO': 12,
        'THRESHOLD_PERCENTAGE': 70,
        'MAX_POC_COUNT': 2000,
        'MIN_POC_COUNT': 200,
        'CONTROL_LIMIT': 5,
        'UPPER_LIFT_BOUND': 300,
        'LOWER_LIFT_BOUND': -300
    }
    
    actual_values = {
        'RESTRICT_BASELINE_TO': RESTRICT_BASELINE_TO,
        'THRESHOLD_PERCENTAGE': THRESHOLD_PERCENTAGE,
        'MAX_POC_COUNT': MAX_POC_COUNT,
        'MIN_POC_COUNT': MIN_POC_COUNT,
        'CONTROL_LIMIT': CONTROL_LIMIT,
        'UPPER_LIFT_BOUND': UPPER_LIFT_BOUND,
        'LOWER_LIFT_BOUND': LOWER_LIFT_BOUND
    }
    
    for key, expected in expected_values.items():
        actual = actual_values[key]
        if actual == expected:
            print(f"✅ {key}: {actual} (matches expected)")
        else:
            print(f"❌ {key}: {actual} (expected {expected})")
            return False
    
    return True

def test_import_structure():
    """Test that the import structure works as expected."""
    print("\nTesting import structure...")
    
    # Test package-level imports
    try:
        from accelerate_modules import run_accelerate_pipeline, accelerate, E_path
        print("✅ Package-level imports work")
    except ImportError as e:
        print(f"❌ Package-level import failed: {e}")
        return False
    
    # Test module-specific imports
    try:
        from accelerate_modules.main import accelerate
        from accelerate_modules.config import get_activity_df
        from accelerate_modules.data_processing import create_pilot_df
        from accelerate_modules.analysis import get_uplift
        from accelerate_modules.utils import find_date
        print("✅ Module-specific imports work")
    except ImportError as e:
        print(f"❌ Module-specific import failed: {e}")
        return False
    
    return True

def main():
    """Run all comparison tests."""
    print("=" * 60)
    print("ACCELERATE MODULAR VS ORIGINAL - COMPARISON TESTS")
    print("=" * 60)
    
    tests = [
        ("Individual Functions Test", test_individual_functions),
        ("Function Signatures Test", compare_function_signatures),
        ("Data Structures Test", test_data_structures),
        ("Import Structure Test", test_import_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("COMPARISON TEST RESULTS")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("The modular version is structurally identical to the original.")
        print("You can now run the modular version with confidence that it will")
        print("produce the same output as the original Accelerate.py file.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please review the failed tests above before using the modular version.")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 READY TO USE!")
        print("To run the modular version:")
        print("  python Accelerate_Modular.py")
        print("\nOr import and use specific functions:")
        print("  from accelerate_modules.main import run_accelerate_pipeline")
        print("  run_accelerate_pipeline()")
