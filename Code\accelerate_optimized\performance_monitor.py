"""
Performance monitoring utilities for the optimized Accelerate pipeline.
Provides timing, memory usage tracking, progress bars, and performance metrics.

Features:
- Execution time tracking
- Memory usage monitoring
- Progress bars with ETA
- Performance profiling
- Resource utilization tracking
- Bottleneck identification
"""

import time
import psutil
import gc
import functools
import threading
from contextlib import contextmanager
from collections import defaultdict, deque
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from tqdm import tqdm
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('accelerate_performance.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Comprehensive performance monitoring class."""
    
    def __init__(self):
        self.start_time = None
        self.timers = defaultdict(list)
        self.memory_snapshots = deque(maxlen=1000)
        self.counters = defaultdict(int)
        self.progress_bars = {}
        self.resource_monitor_active = False
        self.resource_thread = None
        
    def start_monitoring(self):
        """Start overall performance monitoring."""
        self.start_time = time.time()
        self.start_resource_monitoring()
        logger.info("Performance monitoring started")
        
    def stop_monitoring(self):
        """Stop performance monitoring and generate report."""
        if self.start_time:
            total_time = time.time() - self.start_time
            self.stop_resource_monitoring()
            logger.info(f"Total execution time: {total_time:.2f} seconds")
            return self.generate_report()
        return None
    
    def start_resource_monitoring(self):
        """Start background resource monitoring."""
        self.resource_monitor_active = True
        self.resource_thread = threading.Thread(target=self._monitor_resources)
        self.resource_thread.daemon = True
        self.resource_thread.start()
    
    def stop_resource_monitoring(self):
        """Stop background resource monitoring."""
        self.resource_monitor_active = False
        if self.resource_thread:
            self.resource_thread.join(timeout=1)
    
    def _monitor_resources(self):
        """Background thread for monitoring system resources."""
        while self.resource_monitor_active:
            try:
                memory_info = psutil.virtual_memory()
                cpu_percent = psutil.cpu_percent()
                
                snapshot = {
                    'timestamp': time.time(),
                    'memory_used_gb': memory_info.used / (1024**3),
                    'memory_percent': memory_info.percent,
                    'cpu_percent': cpu_percent,
                    'available_memory_gb': memory_info.available / (1024**3)
                }
                self.memory_snapshots.append(snapshot)
                time.sleep(1)  # Monitor every second
            except Exception as e:
                logger.warning(f"Resource monitoring error: {e}")
                break
    
    @contextmanager
    def timer(self, name):
        """Context manager for timing operations."""
        start_time = time.time()
        start_memory = psutil.virtual_memory().used / (1024**3)
        
        try:
            yield
        finally:
            end_time = time.time()
            end_memory = psutil.virtual_memory().used / (1024**3)
            
            duration = end_time - start_time
            memory_delta = end_memory - start_memory
            
            self.timers[name].append({
                'duration': duration,
                'memory_delta': memory_delta,
                'timestamp': start_time
            })
            
            logger.debug(f"{name}: {duration:.3f}s, Memory Δ: {memory_delta:.3f}GB")
    
    def create_progress_bar(self, name, total, desc=None):
        """Create a progress bar for tracking operations."""
        if desc is None:
            desc = name
        
        pbar = tqdm(
            total=total,
            desc=desc,
            unit='items',
            ncols=100,
            bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]'
        )
        self.progress_bars[name] = pbar
        return pbar
    
    def update_progress(self, name, increment=1):
        """Update progress bar."""
        if name in self.progress_bars:
            self.progress_bars[name].update(increment)
    
    def close_progress_bar(self, name):
        """Close and remove progress bar."""
        if name in self.progress_bars:
            self.progress_bars[name].close()
            del self.progress_bars[name]
    
    def increment_counter(self, name, value=1):
        """Increment a named counter."""
        self.counters[name] += value
    
    def get_memory_usage(self):
        """Get current memory usage in GB."""
        return psutil.virtual_memory().used / (1024**3)
    
    def get_peak_memory_usage(self):
        """Get peak memory usage from snapshots."""
        if not self.memory_snapshots:
            return self.get_memory_usage()
        return max(snapshot['memory_used_gb'] for snapshot in self.memory_snapshots)
    
    def force_garbage_collection(self):
        """Force garbage collection and log memory freed."""
        before_memory = self.get_memory_usage()
        gc.collect()
        after_memory = self.get_memory_usage()
        freed = before_memory - after_memory
        
        if freed > 0.1:  # Only log if significant memory was freed
            logger.info(f"Garbage collection freed {freed:.2f}GB")
        
        self.increment_counter('gc_runs')
        return freed
    
    def generate_report(self):
        """Generate comprehensive performance report."""
        report = {
            'execution_summary': self._get_execution_summary(),
            'timing_breakdown': self._get_timing_breakdown(),
            'memory_analysis': self._get_memory_analysis(),
            'resource_utilization': self._get_resource_utilization(),
            'bottlenecks': self._identify_bottlenecks(),
            'recommendations': self._get_recommendations()
        }
        
        # Save report to file
        self._save_report(report)
        return report
    
    def _get_execution_summary(self):
        """Get execution summary statistics."""
        total_time = time.time() - self.start_time if self.start_time else 0
        
        return {
            'total_execution_time': total_time,
            'peak_memory_usage_gb': self.get_peak_memory_usage(),
            'total_operations': sum(self.counters.values()),
            'operations_per_second': sum(self.counters.values()) / max(total_time, 1),
            'garbage_collections': self.counters.get('gc_runs', 0)
        }
    
    def _get_timing_breakdown(self):
        """Get detailed timing breakdown by operation."""
        breakdown = {}
        
        for operation, times in self.timers.items():
            if times:
                durations = [t['duration'] for t in times]
                breakdown[operation] = {
                    'total_time': sum(durations),
                    'average_time': np.mean(durations),
                    'min_time': min(durations),
                    'max_time': max(durations),
                    'count': len(durations),
                    'std_dev': np.std(durations)
                }
        
        return breakdown
    
    def _get_memory_analysis(self):
        """Analyze memory usage patterns."""
        if not self.memory_snapshots:
            return {'error': 'No memory snapshots available'}
        
        memory_values = [s['memory_used_gb'] for s in self.memory_snapshots]
        
        return {
            'peak_memory_gb': max(memory_values),
            'average_memory_gb': np.mean(memory_values),
            'min_memory_gb': min(memory_values),
            'memory_variance': np.var(memory_values),
            'memory_trend': 'increasing' if memory_values[-1] > memory_values[0] else 'decreasing'
        }
    
    def _get_resource_utilization(self):
        """Get resource utilization statistics."""
        if not self.memory_snapshots:
            return {'error': 'No resource data available'}
        
        cpu_values = [s['cpu_percent'] for s in self.memory_snapshots if 'cpu_percent' in s]
        
        return {
            'average_cpu_percent': np.mean(cpu_values) if cpu_values else 0,
            'peak_cpu_percent': max(cpu_values) if cpu_values else 0,
            'cpu_utilization_efficiency': 'high' if np.mean(cpu_values) > 70 else 'moderate' if np.mean(cpu_values) > 30 else 'low'
        }
    
    def _identify_bottlenecks(self):
        """Identify performance bottlenecks."""
        bottlenecks = []
        
        # Identify slow operations
        for operation, times in self.timers.items():
            if times:
                avg_time = np.mean([t['duration'] for t in times])
                if avg_time > 10:  # Operations taking more than 10 seconds
                    bottlenecks.append({
                        'type': 'slow_operation',
                        'operation': operation,
                        'average_time': avg_time,
                        'severity': 'high' if avg_time > 60 else 'medium'
                    })
        
        # Check memory usage
        peak_memory = self.get_peak_memory_usage()
        total_memory = psutil.virtual_memory().total / (1024**3)
        
        if peak_memory > total_memory * 0.8:
            bottlenecks.append({
                'type': 'high_memory_usage',
                'peak_memory_gb': peak_memory,
                'memory_utilization_percent': (peak_memory / total_memory) * 100,
                'severity': 'high'
            })
        
        return bottlenecks
    
    def _get_recommendations(self):
        """Generate performance optimization recommendations."""
        recommendations = []
        
        # Analyze timing data for recommendations
        timing_breakdown = self._get_timing_breakdown()
        
        for operation, stats in timing_breakdown.items():
            if stats['average_time'] > 30:
                recommendations.append(f"Consider optimizing '{operation}' - average time: {stats['average_time']:.2f}s")
        
        # Memory recommendations
        peak_memory = self.get_peak_memory_usage()
        if peak_memory > 8:  # More than 8GB
            recommendations.append("High memory usage detected - consider processing data in smaller chunks")
        
        # CPU utilization recommendations
        resource_util = self._get_resource_utilization()
        if 'average_cpu_percent' in resource_util and resource_util['average_cpu_percent'] < 30:
            recommendations.append("Low CPU utilization - consider increasing parallel processing")
        
        return recommendations
    
    def _save_report(self, report):
        """Save performance report to file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"performance_report_{timestamp}.json"
        
        try:
            import json
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            logger.info(f"Performance report saved to {filename}")
        except Exception as e:
            logger.error(f"Failed to save performance report: {e}")

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

# Decorator for automatic timing
def timed_operation(operation_name=None):
    """Decorator to automatically time function execution."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            name = operation_name or func.__name__
            with performance_monitor.timer(name):
                return func(*args, **kwargs)
        return wrapper
    return decorator

# Context manager for memory monitoring
@contextmanager
def memory_monitor(operation_name):
    """Context manager for monitoring memory usage during operations."""
    start_memory = performance_monitor.get_memory_usage()
    logger.info(f"Starting {operation_name} - Memory: {start_memory:.2f}GB")
    
    try:
        yield
    finally:
        end_memory = performance_monitor.get_memory_usage()
        delta = end_memory - start_memory
        logger.info(f"Completed {operation_name} - Memory: {end_memory:.2f}GB (Delta{delta:+.2f}GB)")
        
        if delta > 1.0:  # If memory increased by more than 1GB
            logger.warning(f"High memory increase detected in {operation_name}")

def log_system_info():
    """Log current system information."""
    memory = psutil.virtual_memory()
    cpu_count = psutil.cpu_count()
    
    logger.info(f"System Info - CPU Cores: {cpu_count}, Total Memory: {memory.total/(1024**3):.1f}GB, Available: {memory.available/(1024**3):.1f}GB")
