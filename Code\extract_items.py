import pandas as pd
import os

# CSV file name
input_csv = "Combined_Sales_Asda_Master.csv"

# Output folder
output_folder = "SKU DATA"
os.makedirs(output_folder, exist_ok=True)

# Updated list of item codes from your screenshot (as strings)
item_codes = [
    "2000005803971",
    "2000140863457",
    "2000024510778",
    "2000198870320",
    "2000095400411",
    "2000103045923",
    "2000041784321",
    "2000041784307",
    "2000041784016",
    "2000042046218",
    "2000041753102",
    "2000041973767",
    "2000041762764",
    "2000041762692",
    "2000041762647",
    "2000041836221",
    "2000041846906",
    "2000155802144",
    "2000059864500",
]

# Read CSV and force Item_Code to string
df = pd.read_csv(input_csv, dtype={'Item_Code': str})
df['Item_Code'] = df['Item_Code'].str.strip()

# Process and export each item code
for code in item_codes:
    filtered_df = df[df['Item_Code'] == code]
    if not filtered_df.empty:
        output_path = os.path.join(output_folder, f"{code}.csv")
        filtered_df.to_csv(output_path, index=False)
        print(f"✅ File created: {output_path} ({len(filtered_df)} rows)")
    else:
        print(f"⚠️ No rows found for Item_Code: {code}")

print("\n✔️ All done. Check the 'SKU DATA' folder.")
