"""
Test script for the modularized Accelerate pipeline.

This script tests the import functionality and basic module operations
without running the full pipeline (which requires data files).
"""

import sys
import traceback

def test_imports():
    """Test all module imports."""
    print("Testing module imports...")
    
    try:
        # Test main package import
        from accelerate_modules import run_accelerate_pipeline, accelerate
        print("✅ Main package import successful")
        
        # Test config module
        from accelerate_modules.config import E_path, get_activity_df, ensure_directories
        print("✅ Config module import successful")
        
        # Test utils module
        from accelerate_modules.utils import find_date, get_data_indices_n_years, get_activation_week_index
        print("✅ Utils module import successful")
        
        # Test data processing module
        from accelerate_modules.data_processing import create_pilot_df, get_sku_list, create_val_data, mod1, read_data
        print("✅ Data processing module import successful")
        
        # Test analysis module
        from accelerate_modules.analysis import (
            testvscontrolfcn, get_dist_mat_grp, get_optimal_n_cluster, 
            get_clustered_data, filter_control_pocs, get_uplift, 
            get_uplift_val, lift_outlier_iqr, significance_level
        )
        print("✅ Analysis module import successful")
        
        # Test main module
        from accelerate_modules.main import accelerate, run_accelerate_pipeline
        print("✅ Main module import successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_basic_functionality():
    """Test basic functionality without requiring data files."""
    print("\nTesting basic functionality...")
    
    try:
        # Test config functions
        from accelerate_modules.config import ensure_directories, E_path
        print(f"✅ E_path configured: {E_path}")
        
        # Test utils functions with sample data
        from accelerate_modules.utils import find_date
        import pandas as pd
        from datetime import datetime
        
        min_date = datetime(2023, 1, 1)
        max_date = datetime(2023, 12, 31)
        min_col, max_col = find_date(min_date, max_date)
        print(f"✅ Date formatting works: {min_col} to {max_col}")
        
        # Test that modules have expected functions
        from accelerate_modules import analysis
        expected_functions = ['get_uplift', 'significance_level', 'testvscontrolfcn']
        for func_name in expected_functions:
            if hasattr(analysis, func_name):
                print(f"✅ Function {func_name} found in analysis module")
            else:
                print(f"❌ Function {func_name} missing from analysis module")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        traceback.print_exc()
        return False

def test_module_structure():
    """Test that the module structure is correct."""
    print("\nTesting module structure...")
    
    try:
        import accelerate_modules
        
        # Check package attributes
        expected_attrs = ['run_accelerate_pipeline', 'accelerate', 'E_path']
        for attr in expected_attrs:
            if hasattr(accelerate_modules, attr):
                print(f"✅ Package attribute {attr} available")
            else:
                print(f"❌ Package attribute {attr} missing")
                return False
        
        # Check version info
        if hasattr(accelerate_modules, '__version__'):
            print(f"✅ Package version: {accelerate_modules.__version__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Module structure test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("ACCELERATE MODULAR PIPELINE - TEST SUITE")
    print("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Basic Functionality Tests", test_basic_functionality),
        ("Module Structure Tests", test_module_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! The modular structure is working correctly.")
        print("You can now use the modularized version with confidence.")
    else:
        print("❌ SOME TESTS FAILED! Please check the errors above.")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
