"""
Optimized analysis module for the Accelerate analysis pipeline.
Implements parallel clustering, optimized distance calculations, and batch processing.

Performance Improvements:
- Parallel distance matrix calculations
- Optimized DTW parameters
- Vectorized statistical operations
- Memory-efficient clustering algorithms
- Batch processing for large datasets
- Cached intermediate results
"""

import pandas as pd
import numpy as np
import time
import math
import sklearn.metrics
from sklearn.metrics import silhouette_score
from sklearn.cluster import KMeans, MiniBatchKMeans
from dtaidistance import dtw
from scipy.stats import wilcoxon
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor, as_completed
from functools import lru_cache
import warnings

from .config_optimized import (
    E_path, UPPER_LIFT_BOUND, LOWER_LIFT_BOUND, ALPHA_SIGNIFICANCE,
    DTW_PARALLEL, DTW_WINDOW_SIZE, DTW_STEP_PATTERN, DISTANCE_MATRIX_DTYPE,
    KMEANS_INIT, KMEANS_N_INIT, K<PERSON><PERSON><PERSON>_MAX_ITER, KMEANS_TOL,
    USE_MINI_BATCH_KMEANS, MINI_BATCH_SIZE, MAX_WORKERS, PARALLEL_CLUSTERING
)
from .performance_monitor import performance_monitor, timed_operation, memory_monitor

# Suppress sklearn warnings for cleaner output
warnings.filterwarnings('ignore', category=UserWarning, module='sklearn')

@timed_operation("testvscontrol_optimized")
def testvscontrolfcn_optimized(control_data, test_data):
    """
    Optimized version of testvscontrolfcn with vectorized operations.
    """
    with memory_monitor("testvscontrol_comparison"):
        # Drop known non-numeric or irrelevant columns
        drop_columns = ['Start_Date', 'End_Date', 'NET_AVG_Y1', 'POC_ID']
        control_data = control_data.drop(columns=[col for col in drop_columns if col in control_data.columns])
        test_data = test_data.drop(columns=[col for col in drop_columns if col in test_data.columns])

        # Vectorized replacement of 0 with NaN
        control_data = control_data.replace(0, np.nan)
        test_data = test_data.replace(0, np.nan)

        # Select only numeric columns
        control_data = control_data.select_dtypes(include='number')
        test_data = test_data.select_dtypes(include='number')

        # Vectorized mean calculations
        test_mean = test_data.mean(axis=0, skipna=True).to_frame().transpose()
        control_mean = control_data.mean(axis=0, skipna=True).to_frame().transpose()

        # Align column order
        control_mean = control_mean[test_mean.columns]

        # Efficient concatenation
        test_control_mean = pd.concat([test_mean, control_mean], ignore_index=True, copy=False)
        test_control_mean.rename(index={0: 'Test', 1: 'Control'}, inplace=True)

        return test_control_mean

def calculate_distance_matrix_chunk(data_chunk, chunk_id):
    """
    Calculate distance matrix for a data chunk (for parallel processing).
    """
    try:
        np.random.seed(47 + chunk_id)  # Ensure reproducibility with different seeds
        
        # Configure DTW parameters for optimization
        dtw_config = {
            'parallel': DTW_PARALLEL,
            'window': DTW_WINDOW_SIZE,
            'step_pattern': DTW_STEP_PATTERN
        }
        
        # Remove None values from config
        dtw_config = {k: v for k, v in dtw_config.items() if v is not None}
        
        _dm = dtw.distance_matrix(data_chunk.values, **dtw_config)
        
        # Vectorized NaN and Inf handling
        _dm = np.nan_to_num(_dm, nan=0.0, posinf=0.0, neginf=0.0)
        
        # Convert to memory-efficient data type
        if DISTANCE_MATRIX_DTYPE != np.float64:
            _dm = _dm.astype(DISTANCE_MATRIX_DTYPE)
        
        return _dm, chunk_id
        
    except Exception as e:
        print(f"Error in calculating distance matrix for chunk {chunk_id}: {e}")
        return None, chunk_id

@timed_operation("distance_matrix_parallel")
def get_dist_mat_grp_optimized(dm_data_grp):
    """
    Optimized version with parallel distance matrix calculation.
    """
    dist_mat_grp = []
    
    if PARALLEL_CLUSTERING and len(dm_data_grp) > 1:
        # Parallel processing for multiple groups
        with ThreadPoolExecutor(max_workers=min(MAX_WORKERS, len(dm_data_grp))) as executor:
            future_to_idx = {
                executor.submit(calculate_distance_matrix_chunk, dm_vg, idx): idx 
                for idx, dm_vg in enumerate(dm_data_grp)
            }
            
            # Collect results in order
            results = [None] * len(dm_data_grp)
            for future in as_completed(future_to_idx):
                result, idx = future.result()
                if result is not None:
                    results[idx] = result
                    print(f"[DM] Completed Volume-Group-{idx + 1}")
            
            dist_mat_grp = [r for r in results if r is not None]
    else:
        # Sequential processing for single group or when parallel is disabled
        for idx, dm_vg in enumerate(dm_data_grp):
            result, _ = calculate_distance_matrix_chunk(dm_vg, idx)
            if result is not None:
                dist_mat_grp.append(result)
                print(f"[DM] Completed Volume-Group-{idx + 1}")
    
    return dist_mat_grp

@timed_operation("optimal_clustering")
def get_optimal_n_cluster_optimized(dist_mat_grp):
    """
    Optimized version with better clustering algorithm selection.
    """
    num_clusters_grp = []
    
    for idx, X in enumerate(dist_mat_grp):
        t1 = time.time()
        max_sil_score = -1
        opt_clus = 1
        
        # Determine max clusters based on data size
        max_clusters = min(10, max(2, len(X) // 10))
        
        for n_cluster in range(1, max_clusters + 1):
            try:
                # Use MiniBatchKMeans for large datasets
                if USE_MINI_BATCH_KMEANS and len(X) > MINI_BATCH_SIZE:
                    kmeans = MiniBatchKMeans(
                        n_clusters=n_cluster,
                        random_state=47,
                        batch_size=min(MINI_BATCH_SIZE, len(X) // 2),
                        init=KMEANS_INIT,
                        n_init=KMEANS_N_INIT,
                        max_iter=KMEANS_MAX_ITER,
                        tol=KMEANS_TOL
                    ).fit(X)
                else:
                    kmeans = KMeans(
                        n_clusters=n_cluster,
                        random_state=47,
                        init=KMEANS_INIT,
                        n_init=KMEANS_N_INIT,
                        max_iter=KMEANS_MAX_ITER,
                        tol=KMEANS_TOL
                    ).fit(X)
                
                label = kmeans.labels_
                
                # Only calculate silhouette score for more than 1 cluster
                if n_cluster > 1:
                    sil_coeff = silhouette_score(X, label, metric='euclidean')
                    
                    if sil_coeff > max_sil_score:
                        max_sil_score = sil_coeff
                        opt_clus = n_cluster
                    
                    print(f"\tFor n_clusters={n_cluster}, The Silhouette Coefficient is {sil_coeff:.4f}")
                
            except Exception as e:
                print(f"\tError in finding optimal cluster for Group-{idx + 1}: {e}")
        
        num_clusters_grp.append(opt_clus)
        
        elapsed_time = time.time() - t1
        print(f"[Volume-Group-{idx + 1}] | Optimal n-Cluster = {opt_clus} | Took {elapsed_time:.2f} seconds")
        print("-" * 75)
    
    return num_clusters_grp

@timed_operation("clustering_data")
def get_clustered_data_optimized(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, DATA):
    """
    Optimized version with memory-efficient clustering.
    """
    clustered_data_grp = []
    
    for idx, _dm in enumerate(dist_mat_grp):
        t1 = time.time()
        
        n_clusters = num_clusters_grp[idx]
        
        # Use appropriate clustering algorithm based on data size
        if USE_MINI_BATCH_KMEANS and len(_dm) > MINI_BATCH_SIZE:
            kmeans = MiniBatchKMeans(
                n_clusters=n_clusters,
                random_state=47,
                batch_size=min(MINI_BATCH_SIZE, len(_dm) // 2),
                init=KMEANS_INIT,
                n_init=KMEANS_N_INIT,
                max_iter=KMEANS_MAX_ITER,
                tol=KMEANS_TOL
            ).fit(_dm)
        else:
            kmeans = KMeans(
                n_clusters=n_clusters,
                random_state=47,
                init=KMEANS_INIT,
                n_init=KMEANS_N_INIT,
                max_iter=KMEANS_MAX_ITER,
                tol=KMEANS_TOL
            ).fit(_dm)
        
        kmeans_grp.append(kmeans)
        _labels = kmeans.predict(_dm)
        
        # Efficient data filtering and assignment
        poc_mask = DATA['POC_ID'].isin(poc_ids_grp[idx])
        _data = DATA[poc_mask].copy()
        _data['Cluster'] = _labels.tolist()
        
        clustered_data_grp.append(_data)
        
        elapsed_time = time.time() - t1
        print(f"Clustering Group-{idx + 1} completed in {elapsed_time:.2f} seconds")
    
    return clustered_data_grp

@timed_operation("filter_control_pocs")
def filter_control_pocs_optimized(global_ctrl, global_test, min_index, activation_on, limit):
    """
    Optimized version with vectorized RMSE calculation.
    """
    with memory_monitor("control_poc_filtering"):
        # Extract baseline data efficiently
        ctrl_baseline = global_ctrl.iloc[:, min_index:activation_on].values
        test_baseline = global_test.iloc[:, min_index:activation_on].values
        
        # Get test baseline (first row)
        predicted = test_baseline[0, :]
        
        # Vectorized RMSE calculation
        mse_values = np.mean((ctrl_baseline - predicted) ** 2, axis=1)
        rmse_values = np.sqrt(mse_values)
        
        # Create control dataframe with RMSE values
        control = global_ctrl.copy()
        control["RMSE Value"] = rmse_values
        control["RMSE_Rank"] = control["RMSE Value"].rank(method='min')
        
        # Filter top controls
        control_filtered = control[control["RMSE_Rank"] <= limit]
        
        # Return filtered control data
        new_ctrl = global_ctrl[global_ctrl["POC_ID"].isin(control_filtered["POC_ID"])]
        
        return new_ctrl

@timed_operation("uplift_calculation")
def get_uplift_optimized(desc_test, desc_ctrl, min_index, activation_on, activation_end, test_poc, ctrl_pocs, ctrl_outliers, RESTRICT_BASELINE_TO, APT_RESULTS, campaign, retailer, sku):
    """
    Optimized version of get_uplift with vectorized calculations.
    """
    activation_on = activation_on - min_index
    if isinstance(test_poc, pd.Series) or isinstance(test_poc, np.ndarray):
        test_poc = int(test_poc.iloc[0] if hasattr(test_poc, 'iloc') else test_poc[0])

    if len(desc_test) == len(desc_ctrl):
        # Vectorized baseline and analysis period calculations
        baseline_start = max(0, activation_on - RESTRICT_BASELINE_TO)
        analysis_end_index = (activation_end - min_index) + 1

        # Extract periods using vectorized operations
        baseline_test = desc_test[baseline_start:activation_on]
        baseline_ctrl = desc_ctrl[baseline_start:activation_on]
        analysis_test = desc_test[activation_on:analysis_end_index]
        analysis_ctrl = desc_ctrl[activation_on:analysis_end_index]

        # Vectorized calculations for non-zero counts and sums
        test_baseline_nonzero = np.count_nonzero(baseline_test)
        ctrl_baseline_nonzero = np.count_nonzero(baseline_ctrl)
        test_analysis_nonzero = np.count_nonzero(analysis_test)
        ctrl_analysis_nonzero = np.count_nonzero(analysis_ctrl)

        # Calculate averages efficiently
        avg_test_y1 = np.sum(baseline_test) / max(test_baseline_nonzero, 1)
        avg_ctrl_y1 = np.sum(baseline_ctrl) / max(ctrl_baseline_nonzero, 1)
        avg_test_y2 = np.sum(analysis_test) / max(test_analysis_nonzero, 1)
        avg_ctrl_y2 = np.sum(analysis_ctrl) / max(ctrl_analysis_nonzero, 1)

        # Vectorized percentage calculations
        perc_inc_t = 100 * (avg_test_y2 - avg_test_y1) / max(avg_test_y1, 1e-10)
        perc_inc_c = 100 * (avg_ctrl_y2 - avg_ctrl_y1) / max(avg_ctrl_y1, 1e-10)

        test_expected = avg_test_y1 * (1 + (perc_inc_c / 100))
        lift = 100 * (avg_test_y2 - test_expected) / max(test_expected, 1e-10)
        impact = avg_test_y2 - test_expected

        _avg_vol = (avg_test_y1 + avg_test_y2 + avg_ctrl_y1 + avg_ctrl_y2) / 4
        bs_mths = len(baseline_test)
        as_mths = len(analysis_test)

        # Handle infinite values
        if np.isinf(lift) or np.isnan(lift):
            lift = 0

        # Vectorized goodness of fit calculation
        test_avg = np.mean(desc_test[:-(analysis_end_index - activation_on + 1)])
        ctrl_avg = np.mean(desc_ctrl[:-(analysis_end_index - activation_on + 1)])

        test_dev = (desc_test - test_avg) / max(test_avg, 1e-10)
        ctrl_dev = (desc_ctrl - ctrl_avg) / max(ctrl_avg, 1e-10)

        diff = np.abs(test_dev - ctrl_dev)
        score = np.sum(diff[:-(analysis_end_index - activation_on + 1)])

        # Efficient DataFrame updates using vectorized operations
        test_poc_str = str(test_poc)
        mask = APT_RESULTS['POC_ID'].astype(str) == test_poc_str

        try:
            # Batch update all columns at once
            update_dict = {
                'ABI-TEST AVG VOL': (avg_test_y1 + avg_test_y2) / 2,
                'ABI-AVG VOL': _avg_vol,
                'ABI-%Lift': lift,
                'ABI-Estimated impact': impact,
                'ABI-Test baseline period': avg_test_y1,
                'ABI-Test analysis period': avg_test_y2,
                'ABI-Test expected': test_expected,
                'ABI-%Test performance': perc_inc_t,
                'ABI-Control baseline period': avg_ctrl_y1,
                'ABI-Control analysis period': avg_ctrl_y2,
                'ABI-%Control performance': perc_inc_c,
                'ABI-Baseline period # weeks with data': bs_mths,
                'ABI-Analysis period # weeks with data': as_mths,
                'ABI-Control count': len(ctrl_pocs),
                'ABI-Control Outliers': ctrl_outliers,
                'ABI-Goodness of fit score': score,
                'Campaign': campaign,
                'Retailer': retailer,
                'SKU': sku
            }

            for col, value in update_dict.items():
                APT_RESULTS.loc[mask, col] = value

        except Exception as e:
            print("Error in uplift calculation:", e)

        APT_RESULTS.to_excel(E_path + "\\APT_test.xlsx")
    else:
        print("ERROR : Test and Control are not having same number of columns")

    print(perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol)
    return [perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol]

@timed_operation("validation_uplift")
def get_uplift_val_optimized(desc_test, desc_ctrl, min_index, activation_on, validation_start, validation_end, test_poc, ctrl_pocs, RESTRICT_BASELINE_TO, APT_RESULTS):
    """
    Optimized version of get_uplift_val with vectorized operations.
    """
    if isinstance(test_poc, pd.Series) or isinstance(test_poc, np.ndarray):
        test_poc = int(test_poc.iloc[0] if hasattr(test_poc, 'iloc') else test_poc[0])

    activation_on = activation_on - min_index

    if len(desc_test) == len(desc_ctrl):
        # Vectorized calculations similar to get_uplift_optimized
        baseline_start = max(0, activation_on - RESTRICT_BASELINE_TO)
        analysis_end_index = (validation_end - min_index) + 1

        # Extract periods
        baseline_test = desc_test[baseline_start:activation_on]
        baseline_ctrl = desc_ctrl[baseline_start:activation_on]
        analysis_test = desc_test[activation_on:analysis_end_index]
        analysis_ctrl = desc_ctrl[activation_on:analysis_end_index]

        # Vectorized non-zero counts and averages
        test_baseline_nonzero = np.count_nonzero(baseline_test)
        ctrl_baseline_nonzero = np.count_nonzero(baseline_ctrl)
        test_analysis_nonzero = np.count_nonzero(analysis_test)
        ctrl_analysis_nonzero = np.count_nonzero(analysis_ctrl)

        avg_test_y1 = np.sum(baseline_test) / max(test_baseline_nonzero, 1)
        avg_ctrl_y1 = np.sum(baseline_ctrl) / max(ctrl_baseline_nonzero, 1)
        avg_test_y2 = np.sum(analysis_test) / max(test_analysis_nonzero, 1)
        avg_ctrl_y2 = np.sum(analysis_ctrl) / max(ctrl_analysis_nonzero, 1)

        # Calculate lift
        perc_inc_t = 100 * (avg_test_y2 - avg_test_y1) / max(avg_test_y1, 1e-10)
        perc_inc_c = 100 * (avg_ctrl_y2 - avg_ctrl_y1) / max(avg_ctrl_y1, 1e-10)

        test_expected = avg_test_y1 * (1 + (perc_inc_c / 100))
        lift = 100 * (avg_test_y2 - test_expected) / max(test_expected, 1e-10)
        impact = avg_test_y2 - test_expected

        if np.isinf(lift) or np.isnan(lift):
            lift = 0

        # Update results
        test_poc_str = str(test_poc)
        mask = APT_RESULTS['POC_ID'].astype(str) == test_poc_str

        APT_RESULTS.loc[mask, 'ABI-% Validation Period Lift'] = lift
        APT_RESULTS.loc[mask, 'ABI-Validation Period Impact'] = impact
    else:
        print("ERROR : Test and Control are not having same number of columns")

    return [lift]

@timed_operation("outlier_detection")
def lift_outlier_iqr_optimized(data):
    """
    Optimized version of lift_outlier_iqr with vectorized operations.
    """
    # Vectorized outlier detection
    lift_values = data['ABI-%Lift']
    outlier_mask = (lift_values > UPPER_LIFT_BOUND) | (lift_values < LOWER_LIFT_BOUND)

    data['Outlier'] = np.where(outlier_mask, "Yes", "No")
    data['Outlier_Reason'] = np.where(outlier_mask, "Uplift beyond threshold", "")

    # Vectorized null and negative value checks
    columns_to_check = [
        'ABI-Test analysis period', 'ABI-Control analysis period',
        'ABI-Test baseline period', 'ABI-Control baseline period'
    ]

    reasons = [
        "Test site has zero-valued in the analysis period",
        "Control site has zero-valued in the analysis period",
        "Test site has zero-valued in the baseline period",
        "Control site has zero-valued in the baseline period"
    ]

    for col, reason in zip(columns_to_check, reasons):
        if col in data.columns:
            null_mask = data[col].isna()
            negative_mask = data[col] < 0

            data.loc[null_mask, 'Outlier'] = "Yes"
            data.loc[null_mask, 'Outlier_Reason'] = reason

            data.loc[negative_mask, 'Outlier'] = "Yes"
            data.loc[negative_mask, 'Outlier_Reason'] = reason.replace("zero-valued", "negative data")

    # Control count checks
    if 'ABI-Control count' in data.columns:
        control_issues = (data['ABI-Control count'].isna()) | (data['ABI-Control count'] < 5)
        data.loc[control_issues, 'Outlier'] = "Yes"
        data.loc[control_issues, 'Outlier_Reason'] = "Test site has control POCs below 5"

    return data

@timed_operation("significance_testing")
def significance_level_optimized(data, APT_RESULTS, sku):
    """
    Optimized version of significance_level with better error handling.
    """
    # Efficient filtering
    valid_data = data[
        (data["Outlier"] == "No") &
        data["ABI-% Validation Period Lift"].notna()
    ]

    if len(valid_data) < 2:
        print(f"Insufficient data for significance test for SKU: {sku}")
        return 0.0

    # Extract samples
    data1 = valid_data["ABI-% Validation Period Lift"].values
    data2 = valid_data["ABI-%Lift"].values

    print(f"Running Wilcoxon test for SKU: {repr(sku)}")
    print(f"Sample sizes → data1: {len(data1)}, data2: {len(data2)}")

    try:
        # Perform statistical test
        stat, p = wilcoxon(data1, data2)
        print(f"Statistics={stat:.3f}, p={p:.3f}")

        significance_val = (1 - p) * 100
        print(f"Significance level = {significance_val:.2f}%")

        # Efficient update
        if 'Significance' not in APT_RESULTS.columns:
            APT_RESULTS['Significance'] = None

        mask = APT_RESULTS['SKU'].astype(int) == int(sku)
        APT_RESULTS.loc[mask, 'Significance'] = significance_val

        print(APT_RESULTS.loc[mask, ['SKU', 'Significance']])

        # Interpret result
        if p > ALPHA_SIGNIFICANCE:
            print('Same distribution (fail to reject H0)')
        else:
            print('Different distribution (reject H0)')

        return significance_val

    except Exception as e:
        print(f"Error in significance test for SKU {sku}: {e}")
        return 0.0
