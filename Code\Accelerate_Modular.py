"""
Accelerate Analysis Pipeline - Modularized Version

This is the main entry point for the modularized Accelerate analysis pipeline.
It imports and uses the modularized functions to maintain the exact same functionality
as the original monolithic file.

The modularized structure provides:
- Better code organization and maintainability
- Easier testing and debugging
- Reusable components
- Clear separation of concerns

Usage:
    python Accelerate_Modular.py

This will produce exactly the same output as the original Accelerate.py file.
"""

# Import all required libraries (maintaining original imports for compatibility)
import os
import pandas as pd
from scipy.stats import wilcoxon
import pandas as pd
import glob
import numpy as np
import os
import calendar
from termcolor import colored
import datetime
import sys
import re
import warnings
from sklearn.linear_model import LinearRegression
from scipy.stats import ttest_ind
import matplotlib.pyplot as plt
from dtaidistance import dtw
from dtaidistance import clustering
from scipy import stats
import pickle
import sklearn.metrics
import math
import warnings
from scipy.stats import wilcoxon
from scipy.stats import ttest_rel
from datetime import datetime
from scipy.stats import f_oneway
from pandas import Series
import matplotlib.pyplot as plt
import numpy as np
from numpy import inf
import time
from sklearn.metrics import silhouette_score
from sklearn.cluster import KMeans

# Import the modularized pipeline
from accelerate_modules.main import run_accelerate_pipeline

# Set pandas options (maintaining original settings)
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
pd.set_option('display.float_format', '{:.2f}'.format)
warnings.filterwarnings('ignore')

if __name__ == "__main__":
    """
    Main execution block - runs the modularized pipeline.
    
    This maintains the exact same behavior as the original file:
    1. Loads activity data from Excel
    2. Processes each campaign/activity
    3. Generates the same outputs and results
    4. Saves files in the same locations with same formats
    """
    print("Starting Accelerate Analysis Pipeline (Modularized Version)")
    print("=" * 60)
    
    # Run the complete pipeline
    run_accelerate_pipeline()
    
    print("=" * 60)
    print("Accelerate Analysis Pipeline completed successfully!")
    print("All outputs have been generated in the same format as the original version.")
