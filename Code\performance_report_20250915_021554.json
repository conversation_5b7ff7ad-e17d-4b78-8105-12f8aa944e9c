{"execution_summary": {"total_execution_time": 209.60321068763733, "peak_memory_usage_gb": 16.339405059814453, "total_operations": 843, "operations_per_second": 4.0218849569832535, "garbage_collections": 50}, "timing_breakdown": {"create_pilot_df_optimized": {"total_time": 69.78592705726624, "average_time": 1.7020957818845424, "min_time": 1.472181797027588, "max_time": 2.0374667644500732, "count": 41, "std_dev": 0.1289422505836829}, "create_val_data_optimized": {"total_time": 24.04586911201477, "average_time": 0.031975889776615384, "min_time": 0.0, "max_time": 0.8014078140258789, "count": 752, "std_dev": 0.11130082030031242}, "mod1_optimized": {"total_time": 837.3285593986511, "average_time": 2.1862364475160603, "min_time": 0.4387228488922119, "max_time": 3.8876686096191406, "count": 383, "std_dev": 0.7974452411166261}, "read_data_optimized": {"total_time": 48.03290557861328, "average_time": 0.1254122861060399, "min_time": 0.012098550796508789, "max_time": 0.545806884765625, "count": 383, "std_dev": 0.09877591371755251}}, "memory_analysis": {"peak_memory_gb": 16.339405059814453, "average_memory_gb": 16.03915595077887, "min_memory_gb": 15.596118927001953, "memory_variance": 0.009206318109180472, "memory_trend": "increasing"}, "resource_utilization": {"average_cpu_percent": 11.581463414634145, "peak_cpu_percent": 100.0, "cpu_utilization_efficiency": "low"}, "bottlenecks": [], "recommendations": ["High memory usage detected - consider processing data in smaller chunks", "Low CPU utilization - consider increasing parallel processing"]}