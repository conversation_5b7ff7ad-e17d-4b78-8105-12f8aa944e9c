# Accelerate Analysis Pipeline - Modularized Version

## Overview

This is the modularized version of the Accelerate analysis pipeline for retail analytics and campaign effectiveness measurement. The original monolithic `Accelerate.py` file has been refactored into a well-organized, maintainable modular structure while preserving **exactly the same functionality and output**.

## File Structure

```
code/
├── Accelerate.py                    # Original monolithic file
├── Accelerate_Modular.py           # New main entry point (modularized)
├── README_Modular.md               # This documentation
└── accelerate_modules/             # Modularized package
    ├── __init__.py                 # Package initialization
    ├── config.py                   # Configuration and global variables
    ├── utils.py                    # Utility functions
    ├── data_processing.py          # Data loading and preprocessing
    ├── analysis.py                 # Statistical analysis and clustering
    └── main.py                     # Main orchestrator logic
```

## Module Descriptions

### 1. `config.py` - Configuration Module
- **Purpose**: Centralized configuration and global variables
- **Contains**:
  - File paths and directory settings
  - Global constants (thresholds, limits, bounds)
  - Pandas display options
  - Directory creation utilities
- **Key Functions**: `get_activity_df()`, `ensure_directories()`

### 2. `utils.py` - Utility Functions
- **Purpose**: Common utility functions for date handling and data validation
- **Contains**:
  - Date formatting and conversion functions
  - Data index calculation utilities
  - Null value handling functions
- **Key Functions**: `find_date()`, `get_data_indices_n_years()`, `get_activation_week_index()`, `remove_nulls_by_threshold_in_range()`

### 3. `data_processing.py` - Data Processing Module
- **Purpose**: Data loading, cleaning, and preprocessing operations
- **Contains**:
  - Pilot dataframe creation
  - SKU list management
  - Data validation and transformation
  - Data merging and preparation
- **Key Functions**: `create_pilot_df()`, `get_sku_list()`, `create_val_data()`, `mod1()`, `read_data()`

### 4. `analysis.py` - Analysis Module
- **Purpose**: Statistical analysis, clustering, and calculation functions
- **Contains**:
  - Test vs Control comparisons
  - Distance matrix calculations
  - K-means clustering
  - Uplift calculations
  - Statistical significance testing
  - Outlier detection
- **Key Functions**: `testvscontrolfcn()`, `get_dist_mat_grp()`, `get_optimal_n_cluster()`, `get_uplift()`, `significance_level()`

### 5. `main.py` - Main Orchestrator
- **Purpose**: Main pipeline orchestration and execution logic
- **Contains**:
  - Primary `accelerate()` function
  - Pipeline coordination
  - Results consolidation
  - Main execution wrapper
- **Key Functions**: `accelerate()`, `run_accelerate_pipeline()`

## Usage

### Running the Modularized Version

```python
# Method 1: Direct execution
python Accelerate_Modular.py

# Method 2: Import and run
from accelerate_modules.main import run_accelerate_pipeline
run_accelerate_pipeline()

# Method 3: Run specific campaign
from accelerate_modules.main import accelerate
accelerate(start_date, end_date, brand, activity_id, campaign, retailer)
```

### Using Individual Modules

```python
# Import specific functions
from accelerate_modules.data_processing import create_pilot_df, get_sku_list
from accelerate_modules.analysis import get_uplift, significance_level
from accelerate_modules.utils import find_date

# Use functions as needed
pilot_df = create_pilot_df(activity_id, retailer)
sku_list = get_sku_list(retailer, brand)
```

## Key Benefits of Modularization

1. **Maintainability**: Code is organized into logical modules, making it easier to understand and modify
2. **Reusability**: Individual functions can be imported and used independently
3. **Testing**: Each module can be tested separately
4. **Debugging**: Issues can be isolated to specific modules
5. **Collaboration**: Multiple developers can work on different modules simultaneously
6. **Documentation**: Each module has clear purpose and well-documented functions

## Compatibility Guarantee

The modularized version produces **exactly the same output** as the original `Accelerate.py` file:

- ✅ Same input file requirements
- ✅ Same processing logic and algorithms
- ✅ Same output files and formats
- ✅ Same directory structure for results
- ✅ Same statistical calculations and metrics
- ✅ Same error handling behavior

## Dependencies

The modularized version uses the same dependencies as the original:

```
pandas
numpy
scipy
scikit-learn
matplotlib
dtaidistance
termcolor
```

## Future Improvements

With the modular structure in place, you can now easily:

1. **Add new analysis methods** by extending the `analysis.py` module
2. **Support new data sources** by adding functions to `data_processing.py`
3. **Implement new visualizations** by creating a dedicated `visualization.py` module
4. **Add configuration options** by extending `config.py`
5. **Create unit tests** for individual modules
6. **Add logging and monitoring** capabilities
7. **Implement parallel processing** for better performance

## Migration Notes

- The original `Accelerate.py` file remains unchanged and functional
- `Accelerate_Modular.py` is the new entry point that uses the modular structure
- All existing workflows and dependencies remain the same
- No changes required to input data formats or file locations

## Support

For questions or issues with the modularized version, please refer to:
1. This README file for structure and usage information
2. Individual module docstrings for function-specific documentation
3. The original `Accelerate.py` file for reference implementation
