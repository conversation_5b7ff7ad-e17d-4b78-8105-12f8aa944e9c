import pandas as pd
import numpy as np
from datetime import datetime
import time
import re


def setup_pandas_options():
    """Setup pandas display options extracted from Accelerate_Auto1.py lines 35-37"""
    pd.set_option('display.max_columns', None)
    pd.set_option('display.max_rows', None)
    pd.set_option('display.float_format', '{:.2f}'.format)


def print_timestamp(message):
    """Print message with current timestamp prefix"""
    current_datetime = datetime.now()
    formatted_timestamp = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{formatted_timestamp}] {message}")


def validate_date_format(date_string):
    """
    Validate if a string is in a valid date format
    
    Args:
        date_string (str): Date string to validate
        
    Returns:
        bool: True if valid date format, False otherwise
    """
    try:
        datetime.strptime(date_string, '%Y-%m-%d')
        return True
    except ValueError:
        return False


def safe_numeric_conversion(value, default=0):
    """
    Safely convert a value to numeric type, returning default on failure
    
    Args:
        value: Value to convert to numeric
        default: Default value to return if conversion fails
        
    Returns:
        Numeric value or default
    """
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def log_processing_step(step_name, duration):
    """
    Log the duration of a processing step
    
    Args:
        step_name (str): Name of the processing step
        duration (float): Duration in seconds
    """
    print_timestamp(f"Step '{step_name}' completed in {duration:.2f} seconds")