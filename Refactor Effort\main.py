import pandas as pd
from config import Config
from data_loader import create_pilot_df, get_sku_list
from data_processor import mod1, create_val_data
from control_matcher import match_controls
from uplift_calculator import calculate_uplift
from statistical_analyzer import lift_outlier_iqr, significance_level
from result_exporter import (
    generate_test_control_plot,
    export_results_to_excel,
    export_control_mapping,
    create_output_directories
)
from utils import find_date, remove_nulls_by_threshold_in_range, replace_nulls_with_0, get_data_indices_n_years

def process_campaign(activity_id, campaign, retailer, brand, start_date, end_date):
    # Create output directories
    create_output_directories(Config.E_PATH)
    
    # Create pilot dataframe
    meta = create_pilot_df(activity_id, retailer)
    meta['START DATE'] = start_date
    meta['END DATE'] = end_date
    meta.rename(columns={'Store_Id': 'POC_ID'}, inplace=True)
    
    # Process each SKU
    sku_list = get_sku_list(retailer, brand)
    final_results = pd.DataFrame()
    
    for sku in sku_list["ITEM CODE"]:
        try:
            store_df = create_val_data(sku, retailer)
            base_data = mod1(store_df)
            base_data['POC_SAPID'] = pd.to_numeric(base_data['POC_SAPID'], errors='coerce').dropna().astype(int)
            
            # Read and process data
            data, meta_processed = read_data(base_data, meta)
            # ... (rest of processing logic from original accelerate function)
            
            # Perform statistical analysis
            apt_outlier = lift_outlier_iqr(apt_results)
            sig_level = significance_level(apt_outlier, apt_results, sku)
            
            # Export results
            export_control_mapping(test_control_list, activity_id, Config.E_PATH)
            final_results = pd.concat([final_results, apt_results], ignore_index=True)
            
        except Exception as e:
            print(f"Error processing SKU {sku}: {str(e)}")
            continue
    
    # Export final results
    export_results_to_excel(final_results, activity_id, Config.E_PATH)

def main():
    activity_df = pd.read_excel(Config.E_PATH + "\\Activity_ID_List_1.xlsx")
    for _, row in activity_df.iterrows():
        process_campaign(
            row['Activity UID'],
            row['Campaign'],
            row['Retailer'],
            row['Brand'],
            row['Nielsen Start'],
            row['Nielsen End']
        )

if __name__ == "__main__":
    main()