"""
Optimized Configuration module for the Accelerate analysis pipeline.
Contains performance-optimized settings, parallel processing configurations,
and advanced caching options.

Performance Improvements:
- Parallel processing configurations
- Memory management settings
- Caching strategies
- Optimized data types
- Batch processing parameters
"""

import os
import pandas as pd
import numpy as np
import warnings
import multiprocessing as mp
from functools import lru_cache
import psutil

# Suppress warnings
warnings.filterwarnings('ignore')

# Set pandas display options with performance optimizations
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
pd.set_option('display.float_format', '{:.2f}'.format)

# Enable pandas performance optimizations
pd.options.mode.chained_assignment = None  # Disable chained assignment warnings
pd.options.compute.use_bottleneck = True   # Use bottleneck for faster operations
pd.options.compute.use_numexpr = True      # Use numexpr for faster operations

# Global paths
E_path = r"C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Accelerate Improved"

# Global variables with optimized data types
final_result_output = pd.DataFrame()

# Original configuration constants
RESTRICT_BASELINE_TO = 12
THRESHOLD_PERCENTAGE = 70
MAX_POC_COUNT = 2000
MIN_POC_COUNT = 200
CONTROL_LIMIT = 5
UPPER_LIFT_BOUND = 300
LOWER_LIFT_BOUND = -300
ALPHA_SIGNIFICANCE = 0.05

# PERFORMANCE OPTIMIZATION SETTINGS
# =================================

# Parallel Processing Configuration
CPU_COUNT = mp.cpu_count()
MAX_WORKERS = min(CPU_COUNT - 1, 8)  # Leave one CPU free, max 8 workers
PARALLEL_SKU_PROCESSING = True
PARALLEL_CLUSTERING = True
PARALLEL_DISTANCE_MATRIX = True

# Memory Management
MEMORY_LIMIT_GB = psutil.virtual_memory().total / (1024**3) * 0.8  # Use 80% of available RAM
CHUNK_SIZE = 1000  # Process data in chunks to manage memory
ENABLE_GARBAGE_COLLECTION = True
GC_FREQUENCY = 10  # Run garbage collection every N SKUs

# Caching Configuration
ENABLE_CACHING = True
CACHE_SIZE = 128  # LRU cache size for frequently accessed functions
CACHE_DIRECTORY = os.path.join(E_path, 'cache')
ENABLE_DISK_CACHE = True

# Data Processing Optimizations
USE_VECTORIZED_OPERATIONS = True
OPTIMIZE_DATA_TYPES = True
ENABLE_CATEGORICAL_OPTIMIZATION = True
BATCH_SIZE = 50  # Process SKUs in batches

# Distance Matrix Optimizations
DTW_PARALLEL = True
DTW_WINDOW_SIZE = None  # Use None for no window constraint, or set to int for faster computation
DTW_STEP_PATTERN = "symmetric2"  # Optimized step pattern
DISTANCE_MATRIX_DTYPE = np.float32  # Use float32 instead of float64 for memory efficiency

# Clustering Optimizations
KMEANS_INIT = 'k-means++'  # Better initialization
KMEANS_N_INIT = 10  # Number of random initializations
KMEANS_MAX_ITER = 300  # Maximum iterations
KMEANS_TOL = 1e-4  # Tolerance for convergence
USE_MINI_BATCH_KMEANS = True  # Use MiniBatchKMeans for large datasets
MINI_BATCH_SIZE = 1000

# I/O Optimizations
USE_PARQUET = False  # Use Parquet format for faster I/O (if available)
COMPRESSION = 'gzip'  # Compression for file operations
READ_BUFFER_SIZE = 8192  # Buffer size for file reading
WRITE_BUFFER_SIZE = 8192  # Buffer size for file writing

# Progress Tracking
ENABLE_PROGRESS_BAR = True
PROGRESS_UPDATE_FREQUENCY = 1  # Update progress every N items
ENABLE_DETAILED_LOGGING = True
LOG_LEVEL = 'INFO'  # DEBUG, INFO, WARNING, ERROR

# File paths
ACTIVITY_FILE = "Activity_ID_List.xlsx"
TEST_STORE_FILE = "Test Store List.xlsx"
SKU_LIST_FILE = "SKU List.csv"

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING = True
PROFILE_MEMORY_USAGE = True
PROFILE_EXECUTION_TIME = True
SAVE_PERFORMANCE_REPORT = True

@lru_cache(maxsize=1)
def get_activity_df():
    """Load activity dataframe from Excel file with caching."""
    return pd.read_excel(os.path.join(E_path, ACTIVITY_FILE))

@lru_cache(maxsize=1)
def get_system_info():
    """Get system information for optimization."""
    return {
        'cpu_count': CPU_COUNT,
        'memory_gb': psutil.virtual_memory().total / (1024**3),
        'available_memory_gb': psutil.virtual_memory().available / (1024**3),
        'max_workers': MAX_WORKERS
    }

def ensure_directories():
    """Ensure all required directories exist with optimized creation."""
    directories = [
        os.path.join(E_path, 'META'),
        os.path.join(E_path, 'Plots'),
        os.path.join(E_path, 'Clustered_Data'),
        os.path.join(E_path, 'TestvCtrl'),
        os.path.join(E_path, 'Results'),
        CACHE_DIRECTORY if ENABLE_DISK_CACHE else None
    ]
    
    # Filter out None values and create directories in parallel if possible
    directories = [d for d in directories if d is not None]
    
    for directory in directories:
        if not os.path.isdir(directory):
            os.makedirs(directory, exist_ok=True)

def optimize_pandas_settings():
    """Apply pandas performance optimizations."""
    try:
        # Set optimal data types (if available in this pandas version)
        if hasattr(pd.options.mode, 'dtype_backend'):
            pd.options.mode.dtype_backend = "numpy_nullable"

        # Optimize string operations (if available)
        if hasattr(pd.options.future, 'infer_string'):
            pd.options.future.infer_string = True

        # Set memory-efficient options (if available)
        if hasattr(pd.options.mode, 'copy_on_write'):
            pd.options.mode.copy_on_write = True

    except Exception as e:
        # Silently continue if pandas version doesn't support these options
        pass

def get_optimal_chunk_size(data_size_mb):
    """Calculate optimal chunk size based on data size and available memory."""
    available_memory_mb = psutil.virtual_memory().available / (1024**2)
    optimal_chunks = max(1, int(data_size_mb / (available_memory_mb * 0.1)))
    return min(optimal_chunks, 100)  # Cap at 100 chunks

def should_use_parallel_processing(data_size):
    """Determine if parallel processing should be used based on data size."""
    return data_size > 1000 and MAX_WORKERS > 1

# Initialize optimizations
optimize_pandas_settings()
ensure_directories()

# Performance tracking globals
_performance_stats = {
    'total_execution_time': 0,
    'sku_processing_times': [],
    'memory_usage_peak': 0,
    'cache_hits': 0,
    'cache_misses': 0
}

def get_performance_stats():
    """Get current performance statistics."""
    return _performance_stats.copy()

def reset_performance_stats():
    """Reset performance statistics."""
    global _performance_stats
    _performance_stats = {
        'total_execution_time': 0,
        'sku_processing_times': [],
        'memory_usage_peak': 0,
        'cache_hits': 0,
        'cache_misses': 0
    }
