import pandas as pd
import numpy as np
from datetime import datetime

def mod1(store_df):
    """Process store data module 1"""
    data = store_df
    data['POC SAPID'] = data['POC SAPID'].astype(str)
    data = pd.concat([data, data], ignore_index=True)
    list_finaldata = list(data)
    mod_cols = [x.replace(' ', '_') for x in list_finaldata]
    data.columns = mod_cols
    data['Sum(Value)'] = data['Sum(Value)'].apply(lambda x: x if x >= 0 else 0)
    data['Date'] = pd.to_datetime(data['Date'], dayfirst=False)
    data['Year'] = pd.DatetimeIndex(data['Date']).year
    data['Month'] = pd.DatetimeIndex(data['Date']).month
    data['Month'] = data['Month'].astype('str')
    data['Month'] = data.Month.str.pad(2, side='left', fillchar='0')
    data = data.groupby(["POC_SAPID", "Month", "Date"]).agg({"Sum(Value)": "sum"}).reset_index()
    data_groupby = ['POC_SAPID']
    ident_cols = ['POC_SAPID', 'Month', 'Date']
    exp_cols = ['POC_SAPID']
    val_cols = ['Sum(Value)']
    molten_data = pd.melt(data, id_vars=ident_cols, value_vars=val_cols).sort_values(by=['POC_SAPID', 'Month', 'Date'])
    molten_data['MY'] = molten_data['Date'].astype('str')
    molten_data = molten_data.sort_values(by=['POC_SAPID', 'Month', 'Date'])
    Module1_data = molten_data.pivot(index='POC_SAPID', columns='MY', values='value')
    Module1_data.reset_index(inplace=True)
    return Module1_data

def read_data(Base_data, meta):
    """Read and merge base data with metadata"""
    DATA = Base_data  # From the Previous Module
    nd_cols = [x.replace(' ', '_') for x in DATA.columns]
    DATA.columns = nd_cols
    DATA = DATA.rename(columns={'POC_SAPID': 'POC_ID'})
    DATA = DATA.dropna(subset=['POC_ID'])
    DATA.POC_ID = DATA.POC_ID.astype(int)
    DATA.POC_ID = DATA.POC_ID.astype(str)
    META = meta
    
    META = META.rename(columns={'Store number': 'POC_ID', 'START DATE': 'Start_Date', 'END DATE': 'End_Date', 'TestvControl': 'Test_Control'})
    META = META.drop_duplicates(subset=['POC_ID', 'Start_Date', 'End_Date'])
    print("Hi")
    print(META['Test_Control'].unique())
    META['Start_Date'] = pd.to_datetime(META['Start_Date'], dayfirst=True)
    META['End_Date'] = pd.to_datetime(META['End_Date'], dayfirst=True)
    nd_cols = [x.replace(' ', '_') for x in META.columns]
    META.columns = nd_cols
    META = META.dropna(subset=['POC_ID'])
    META.POC_ID = META.POC_ID.astype(int)
    META.POC_ID = META.POC_ID.astype(str)
    
    try:
        DATA = pd.merge(DATA, META[['POC_ID', 'Start_Date', 'End_Date', 'Test_Control']], on=['POC_ID'], how='right')
        d_cols = list(DATA)
        DATA.columns = d_cols
    except Exception as e:
        print("Error in mapping Activation Date to Volume-Data", e)
    return [DATA, META]

def find_date(min_date, max_date):
    """Find formatted date strings"""
    min_month = str(min_date.month).zfill(2)
    min_col = min_date
    min_col = min_date.strftime('%Y-%m-%d')
    max_month = str(max_date.month).zfill(2)
    max_col = max_date
    max_col = max_date.strftime('%Y-%m-%d')
    return (min_col, max_col)

def remove_nulls_by_threshold_in_range(data, threshold, min_index, data_end_index):
    """Remove nulls by threshold in specified range"""
    data_sub = data.iloc[:, min_index:data_end_index].copy()
    _vol_thres = int(threshold * (data_end_index - min_index) / 100)
    data_sub = data_sub.dropna(thresh=_vol_thres)
    return (data[data.index.isin(data_sub.index)])

def replace_nulls_with_0(data, min_index, end_index, columns):
    """Replace null values with 0 in specified range"""
    _vol_data_cols = [columns[x] for x in range(min_index, end_index + 1)]
    data[_vol_data_cols] = data[_vol_data_cols].replace({np.nan: 0})
    return data

def get_data_indices_n_years(columns):
    """Get data indices and year information"""
    date_columns = []
    date_indices = {}
    for i, col in enumerate(columns):
        try:
            dt = datetime.strptime(col, '%Y-%m-%d')
            date_columns.append(dt)
            date_indices[dt] = i
        except:
            continue  # Skip non-date columns like POC_ID, Start_Date, etc.

    if not date_columns:
        raise ValueError("No valid date columns found in the format YYYY-MM-DD.")
    min_date = min(date_columns)
    max_date = max(date_columns)
    index_min = date_indices[min_date]
    index_max = date_indices[max_date]
    return index_min, index_max, min_date.year, max_date.year