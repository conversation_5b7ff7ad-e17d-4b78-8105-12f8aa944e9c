"""
Utility functions for the Accelerate analysis pipeline.
Contains date handling, data validation, and helper functions.
"""

import pandas as pd
import numpy as np
from datetime import datetime


def find_date(min_date, max_date):
    """
    Convert min and max dates to string format.
    
    Args:
        min_date: Minimum date
        max_date: Maximum date
        
    Returns:
        tuple: (min_col, max_col) formatted date strings
    """
    min_col = min_date.strftime('%Y-%m-%d')
    max_col = max_date.strftime('%Y-%m-%d')
    return (min_col, max_col)


def get_data_indices_n_years(columns):
    """
    Get data indices and year range from column names.
    
    Args:
        columns: List of column names
        
    Returns:
        tuple: (index_min, index_max, min_year, max_year)
    """
    date_columns = []
    date_indices = {}
    
    for i, col in enumerate(columns):
        try:
            dt = datetime.strptime(col, '%Y-%m-%d')
            date_columns.append(dt)
            date_indices[dt] = i
        except:
            continue  # Skip non-date columns like POC_ID, Start_Date, etc.

    if not date_columns:
        raise ValueError("No valid date columns found in the format YYYY-MM-DD.")
    
    min_date = min(date_columns)
    max_date = max(date_columns)
    index_min = date_indices[min_date]
    index_max = date_indices[max_date]
    
    return index_min, index_max, min_date.year, max_date.year


def get_activation_week_index(date, test_data_columns):
    """
    Get the index of activation week from test data columns.
    
    Args:
        date: Activation date
        test_data_columns: List of column names
        
    Returns:
        int: Index of activation week
    """
    # Format date to 'YYYY-MM-DD'
    col = date.strftime('%Y-%m-%d')
    
    # Get index of the formatted date
    act_ind = test_data_columns.index(col)
    
    return act_ind


def get_end_week_index(date, test_data_columns):
    """
    Get the index of end week from test data columns.
    
    Args:
        date: End date
        test_data_columns: List of column names
        
    Returns:
        int: Index of end week
    """
    # Format date to 'YYYY-MM-DD'
    col = date.strftime('%Y-%m-%d')
    
    # Get index of the formatted date
    end_ind = test_data_columns.index(col)
    
    return end_ind


def remove_nulls_by_threshold_in_range(data, threshold, min_index, data_end_index):
    """
    Remove rows with nulls exceeding threshold in specified range.
    
    Args:
        data: DataFrame to process
        threshold: Threshold percentage
        min_index: Start index
        data_end_index: End index
        
    Returns:
        DataFrame: Filtered data
    """
    data_sub = data.iloc[:, min_index:data_end_index].copy()
    _vol_thres = int(threshold * (data_end_index - min_index) / 100)
    data_sub = data_sub.dropna(thresh=_vol_thres)
    return data[data.index.isin(data_sub.index)]


def replace_nulls_with_0(data, min_index, end_index, columns):
    """
    Replace null values with 0 in specified range.
    
    Args:
        data: DataFrame to process
        min_index: Start index
        end_index: End index
        columns: List of column names
        
    Returns:
        DataFrame: Data with nulls replaced
    """
    _vol_data_cols = [columns[x] for x in range(min_index, end_index + 1)]
    data[_vol_data_cols] = data[_vol_data_cols].replace({np.nan: 0})
    return data
