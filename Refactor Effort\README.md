# Marketing Campaign Uplift Analysis Tool

## 1. Overview

This tool automates the analysis of marketing campaigns to measure their impact on product sales. It uses a test-and-control methodology, comparing sales data from a "Test" group of stores (where a campaign was active) against a matched "Control" group of stores. The primary output is the "sales uplift"—the increase in sales attributable to the campaign—calculated at the store and SKU level.

The analysis involves:
- Loading and preparing sales and campaign data.
- Identifying a statistically similar control group for each test store using Dynamic Time Warping (DTW) and K-Means clustering.
- Calculating sales lift by comparing sales trends between the test and control groups.
- Performing statistical significance testing (Wilcoxon signed-rank test) to validate the results.
- Generating detailed output reports, including data visualizations.

## 2. Prerequisites

Before running the application, you must install the required Python libraries.

```bash
pip install -r requirements.txt
```

## 3. Directory Structure

The tool expects a specific directory structure for its input files. The base path is configured in `config.py` and must contain the following files and folders:

```
C:/Users/<USER>/OneDrive - Anheuser-Busch InBev/Documents/UK Accelerate Automated/
|
├── Activity_ID_List_1.xlsx         # Main list of campaigns to analyze
├── Test Store List.xlsx            # List of test stores for each campaign
├── SKU List.csv                    # Master list of all SKUs
|
├── Store Codes/
│   ├── {retailer_name_1}.xlsx      # Master list of all stores for a retailer
│   └── {retailer_name_2}.xlsx
|
└── SKU DATA/
    ├── {retailer_name_1}/
    │   ├── {sku_1}.csv             # Weekly sales data for a specific SKU
    │   └── {sku_2}.csv
    └── {retailer_name_2}/
        ├── {sku_3}.csv
        └── {sku_4}.csv
```

## 4. Configuration

All main configuration settings are centralized in the `config.py` file. If your base data path differs from the one specified, you must update the `BASE_PATH` variable in the `Config` class.

```python
# config.py
class Config:
    BASE_PATH = r"C:\Your\Path\To\UK Accelerate Automated"
    # ... other settings
```

## 5. How to Run

To execute the analysis, run the `main.py` script from your terminal. The script will process all campaigns listed in the `Activity_ID_List_1.xlsx` file.

```bash
python main.py
```

## 6. Outputs

The application generates several output files inside the `Refactor Effort` directory:

- **`Results/Final_Results_{activity_id}.xlsx`**: A detailed Excel report for each campaign, containing uplift metrics, significance levels, and outlier status for every test store and SKU.
- **`TestvCtrl/{activity_id}.xlsx`**: A mapping of each test store to its selected control stores.
- **`Plots/{activity_id}.png`**: A line chart visualizing the average sales trends of the test and control groups for each campaign.