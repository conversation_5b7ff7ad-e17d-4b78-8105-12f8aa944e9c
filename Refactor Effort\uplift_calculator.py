import pandas as pd
import numpy as np
import math
import sklearn.metrics

def testvscontrolfcn(control_data, test_data):
    # Drop known non-numeric or irrelevant columns
    drop_columns = ['Start_Date', 'End_Date', 'NET_AVG_Y1', 'POC_ID']
    control_data = control_data.drop(columns=[col for col in drop_columns if col in control_data.columns])
    test_data = test_data.drop(columns=[col for col in drop_columns if col in test_data.columns])

    # Replace 0 with NaN
    control_data = control_data.replace(0, np.nan)
    test_data = test_data.replace(0, np.nan)

    # Select only numeric columns (to avoid TypeError)
    control_data = control_data.select_dtypes(include='number')
    test_data = test_data.select_dtypes(include='number')

    # Compute means
    test_mean = test_data.mean(axis=0, skipna=True).to_frame().transpose()
    control_mean = control_data.mean(axis=0, skipna=True).to_frame().transpose()

    # Align column order (optional but safe)
    control_mean = control_mean[test_mean.columns]

    # Combine test and control means
    test_control_mean = pd.concat([test_mean, control_mean], ignore_index=True)
    test_control_mean.rename(index={0: 'Test', 1: 'Control'}, inplace=True)

    return test_control_mean

def get_activation_week_index(date, test_data_columns):
    # Format date to 'YYYY-MM-DD'
    col = date.strftime('%Y-%m-%d')
    
    # Get index of the formatted date
    act_ind = test_data_columns.index(col)
    
    return act_ind

def get_end_week_index(date, test_data_columns):
    # Format date to 'YYYY-MM-DD'
    col = date.strftime('%Y-%m-%d')
    
    # Get index of the formatted date
    end_ind = test_data_columns.index(col)
    
    return end_ind

def get_uplift(desc_test, desc_ctrl, min_index, activation_on, activation_end, test_poc, ctrl_pocs, ctrl_outliers, RESTRICT_BASELINE_TO, APT_RESULTS, campaign, retailer, sku):
    activation_on = activation_on - min_index
    if isinstance(test_poc, pd.Series) or isinstance(test_poc, np.ndarray):
        test_poc = int(test_poc.iloc[0] if hasattr(test_poc, 'iloc') else test_poc[0])
    if len(desc_test) == len(desc_ctrl):        
        nRecords = len(desc_test)
        sum_ctrl_y1 = 0
        sum_ctrl_y2 = 0
        sum_test_y1 = 0
        sum_test_y2 = 0
        
        ctrl_zero_y1 = 0
        ctrl_zero_y2 = 0
        test_zero_y1 = 0
        test_zero_y2 = 0
        baseline_start = 0
        if activation_on > RESTRICT_BASELINE_TO:
            baseline_start = activation_on - RESTRICT_BASELINE_TO
        for nRec in range(baseline_start, activation_on):
            sum_ctrl_y1 = sum_ctrl_y1 + desc_ctrl[nRec]
            sum_test_y1 = sum_test_y1 + desc_test[nRec]
            if (desc_ctrl[nRec] == 0):
                ctrl_zero_y1 = ctrl_zero_y1 + 1
            if (desc_test[nRec] == 0):
                test_zero_y1 = test_zero_y1 + 1
        analysis_end_index = (activation_end - min_index) + 1  # -3 because 3 columns excluded,poc_id,DATE,End_Date  
        for nRec in range(activation_on, analysis_end_index):
            sum_ctrl_y2 = sum_ctrl_y2 + desc_ctrl[nRec]
            sum_test_y2 = sum_test_y2 + desc_test[nRec]
            if (desc_ctrl[nRec] == 0):
                ctrl_zero_y2 = ctrl_zero_y2 + 1
            if (desc_test[nRec] == 0):
                test_zero_y2 = test_zero_y2 + 1
        desc_ctrl_1 = desc_ctrl[baseline_start:activation_on]
        desc_ctrl_2 = desc_ctrl[activation_on:analysis_end_index]
        desc_test_1 = desc_test[baseline_start:activation_on]
        desc_test_2 = desc_test[activation_on:analysis_end_index]

        ba_count = activation_on - baseline_start
        aa_count = analysis_end_index - activation_on
        
        avg_ctrl_y1 = sum_ctrl_y1 / (ba_count - ctrl_zero_y1) if (ba_count - ctrl_zero_y1) != 0 else 0
        avg_test_y1 = sum_test_y1 / (ba_count - test_zero_y1) if (ba_count - test_zero_y1) != 0 else 0
        
        avg_ctrl_y2 = sum_ctrl_y2 / (aa_count - ctrl_zero_y2) if (aa_count - ctrl_zero_y2) != 0 else 0
        avg_test_y2 = sum_test_y2 / (aa_count - test_zero_y2) if (aa_count - test_zero_y2) != 0 else 0
               
        perc_inc_t = 100 * (avg_test_y2 - avg_test_y1) / avg_test_y1 if avg_test_y1 != 0 else 0
        perc_inc_c = 100 * (avg_ctrl_y2 - avg_ctrl_y1) / avg_ctrl_y1 if avg_ctrl_y1 != 0 else 0
        
        test_expected = avg_test_y1 * (1 + (perc_inc_c / 100)) if avg_test_y1 != 0 else 0
        lift = 100 * (avg_test_y2 - test_expected) / test_expected if test_expected != 0 else 0
        impact = avg_test_y2 - test_expected
               
        _avg_vol = (avg_test_y1 + avg_test_y2 + avg_ctrl_y1 + avg_ctrl_y2) / 4
        bs_mths = desc_ctrl_1.shape[0]
        as_mths = desc_ctrl_2.shape[0]
        if np.isinf(lift):
            lift = 0

        try:
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-TEST AVG VOL'] = (avg_test_y1 + avg_test_y2) / 2
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-AVG VOL'] = _avg_vol
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-%Lift'] = lift
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Estimated impact'] = impact
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Test baseline period'] = avg_test_y1
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Test analysis period'] = avg_test_y2
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Test expected'] = test_expected
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-%Test performance'] = perc_inc_t
        
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control baseline period'] = avg_ctrl_y1
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control analysis period'] = avg_ctrl_y2
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-%Control performance'] = perc_inc_c
        
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Baseline period # weeks with data'] = bs_mths
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Analysis period # weeks with data'] = as_mths
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control count'] = len(ctrl_pocs)
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control Outliers'] = ctrl_outliers
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'Campaign'] = campaign
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'Retailer'] = retailer
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'SKU'] = sku
        except Exception as e:
            print("Error updating APT_RESULTS:", e)
            
    else:
        print("ERROR : Test and Control are not having same number of columns")
    print(perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol)   
    
    return [perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol]

def get_uplift_val(desc_test, desc_ctrl, min_index, activation_on, validation_start, validation_end, test_poc, ctrl_pocs, RESTRICT_BASELINE_TO, APT_RESULTS):
    if isinstance(test_poc, pd.Series) or isinstance(test_poc, np.ndarray):
        test_poc = int(test_poc.iloc[0] if hasattr(test_poc, 'iloc') else test_poc[0])
    activation_on = activation_on - min_index  
    if len(desc_test) == len(desc_ctrl):        
        nRecords = len(desc_test)
        sum_ctrl_y1 = 0
        sum_ctrl_y2 = 0
        sum_test_y1 = 0
        sum_test_y2 = 0
        
        ctrl_zero_y1 = 0
        ctrl_zero_y2 = 0
        test_zero_y1 = 0
        test_zero_y2 = 0
        
        baseline_start = 0
        if activation_on > RESTRICT_BASELINE_TO:
            baseline_start = activation_on - RESTRICT_BASELINE_TO
        for nRec in range(baseline_start, activation_on):
            sum_ctrl_y1 = sum_ctrl_y1 + desc_ctrl[nRec]
            sum_test_y1 = sum_test_y1 + desc_test[nRec]
            if (desc_ctrl[nRec] == 0):
                ctrl_zero_y1 = ctrl_zero_y1 + 1
            if (desc_test[nRec] == 0):
                test_zero_y1 = test_zero_y1 + 1
        analysis_end_index = (validation_end - min_index) + 1  # -3 because 3 columns excluded,poc_id,DATE,End_Date  
        
        for nRec in range(activation_on, analysis_end_index):
            sum_ctrl_y2 = sum_ctrl_y2 + desc_ctrl[nRec]
            sum_test_y2 = sum_test_y2 + desc_test[nRec]
            if (desc_ctrl[nRec] == 0):
                ctrl_zero_y2 = ctrl_zero_y2 + 1
            if (desc_test[nRec] == 0):
                test_zero_y2 = test_zero_y2 + 1
        
        desc_ctrl_1 = desc_ctrl[baseline_start:activation_on]
        desc_ctrl_2 = desc_ctrl[activation_on:analysis_end_index]
        desc_test_1 = desc_test[baseline_start:activation_on]
        desc_test_2 = desc_test[activation_on:analysis_end_index]

        ba_count = activation_on - baseline_start
        aa_count = analysis_end_index - activation_on
               
        avg_ctrl_y1 = sum_ctrl_y1 / (ba_count - ctrl_zero_y1) if (ba_count - ctrl_zero_y1) != 0 else 0
        avg_test_y1 = sum_test_y1 / (ba_count - test_zero_y1) if (ba_count - test_zero_y1) != 0 else 0
        
        avg_ctrl_y2 = sum_ctrl_y2 / (aa_count - ctrl_zero_y2) if (aa_count - ctrl_zero_y2) != 0 else 0
        avg_test_y2 = sum_test_y2 / (aa_count - test_zero_y2) if (aa_count - test_zero_y2) != 0 else 0
               
        perc_inc_t = 100 * (avg_test_y2 - avg_test_y1) / avg_test_y1 if avg_test_y1 != 0 else 0
        perc_inc_c = 100 * (avg_ctrl_y2 - avg_ctrl_y1) / avg_ctrl_y1 if avg_ctrl_y1 != 0 else 0
        
        test_expected = avg_test_y1 * (1 + (perc_inc_c / 100)) if avg_test_y1 != 0 else 0
        lift = 100 * (avg_test_y2 - test_expected) / test_expected if test_expected != 0 else 0
        impact = avg_test_y2 - test_expected
               
        _avg_vol = (avg_test_y1 + avg_test_y2 + avg_ctrl_y1 + avg_ctrl_y2) / 4
        bs_mths = desc_ctrl_1.shape[0]
        as_mths = desc_ctrl_2.shape[0]
        
        if np.isinf(lift):
            lift = 0
        APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-% Validation Period Lift'] = lift
        APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Validation Period Impact'] = impact
        
    else:
        print("ERROR : Test and Control are not having same number of columns")
    
    return [lift]